import { IPosOrder } from "../../app/pos/interface";
import {
    TaxPercentage,
    TobaccoTaxPercentage,
} from "../constants/CommonConstants";
import { OrganizationHelper } from "../helpers/OrganizationHelper";

export class orderUtils {
    private static getAmountsWithTobacco(amount: number, discountRatio = 0) {
        const isTaxable = OrganizationHelper.hasVat();
        let subTotal = isTaxable ? amount / (1 + TaxPercentage) : amount;
        let vat = isTaxable ? subTotal * TaxPercentage : 0;
        let tobaccoTax = 0;

        const tobaccoTaxResult = subTotal / TobaccoTaxPercentage;
        tobaccoTax = tobaccoTaxResult > 25 ? tobaccoTaxResult : 25;
        subTotal -= tobaccoTax;

        const discount = (discountRatio / 100) * subTotal;
        subTotal -= discount;

        if (discountRatio > 0) {
            if (subTotal <= 25) {
                tobaccoTax = 25;
            } else {
                tobaccoTax = subTotal
            }
            vat = (subTotal + tobaccoTax) * TaxPercentage;
        }

        const total = subTotal + vat + tobaccoTax;

        return {
            subTotal: parseFloat(subTotal.toFixed(2)),
            vat: parseFloat(vat.toFixed(2)),
            tobaccoTax: parseFloat(tobaccoTax.toFixed(2)),
            total: parseFloat(total.toFixed(2)),
            discount: parseFloat(discount.toFixed(2)),
        };
    }

    static getAmounts(amount: number, discountRatio = 0) {
        const isTaxable = OrganizationHelper.hasVat();
        let subTotal = isTaxable ? amount / (1 + TaxPercentage) : amount;

        const discount = (discountRatio / 100) * subTotal;
        subTotal -= discount;

        const vat = isTaxable ? subTotal * TaxPercentage : 0;
        const total = subTotal + vat;

        return {
            subTotal: parseFloat(subTotal.toFixed(2)),
            vat: parseFloat(vat.toFixed(2)),
            tobaccoTax: 0,
            total: parseFloat(total.toFixed(2)),
            discount: parseFloat(discount.toFixed(2)),
        };
    }

    private static switchTobaccoTax = (
        price: number,
        discountRatio = 0,
        isTobaccoTax = false,
    ) => {
        if (isTobaccoTax) return this.getAmountsWithTobacco(price, discountRatio);
        return this.getAmounts(price, discountRatio);
    };

    static getPosProductAmounts(
        price: number,
        quantity: number,
        discountRatio = 0,
        isTobaccoTax = false
    ) {
        const { subTotal, vat, total, discount, tobaccoTax } = this.switchTobaccoTax(
            price,
            discountRatio,
            isTobaccoTax
        );

        return {
            subTotal: parseFloat((subTotal * quantity).toFixed(2)),
            vat: parseFloat((vat * quantity).toFixed(2)),
            total: parseFloat((total * quantity).toFixed(2)),
            discount: parseFloat((discount * quantity).toFixed(2)),
            tobaccoTax: parseFloat((tobaccoTax * quantity).toFixed(2)),
        };
    }

    static getPosOrderData(order: IPosOrder) {
        const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();
        const discountRatio = order.selectedDiscount?.amount ?? 0;

        let subTotal = 0;
        let vat = 0;
        let total = 0;
        let discount = 0;
        let tobaccoTax = 0;
        let nativeTotal = 0;
        let nativeDiscount = 0;

        order.products.forEach((el) => {
            const {
                subTotal: productSubTotal,
                vat: productVat,
                total: productTotal,
                discount: productDiscount,
                tobaccoTax: productTobaccoTax,
            } = this.getPosProductAmounts(
                el.price,
                el.quantity,
                discountRatio,
                hasTobaccoTax && el.product.isSubjectToTobaccoTax
            );

            subTotal += productSubTotal;
            vat += productVat;
            total += productTotal; // the total after taxes and discounts
            discount += productDiscount; // the total discount after taxes
            tobaccoTax += productTobaccoTax;
            nativeTotal += el.price * el.quantity; // the total before any taxes or discounts
            nativeDiscount += (el.price * el.quantity * discountRatio) / 100; // the total before any taxes or discounts

            el.additions?.forEach((ad) => {
                const {
                    subTotal: additionSubTotal,
                    vat: additionVat,
                    total: additionTotal,
                    discount: additionDiscount,
                } = this.getAmounts(
                    ad.price * ad.quantity * el.quantity,
                    discountRatio
                );

                subTotal += additionSubTotal;
                vat += additionVat;
                total += additionTotal;
                discount += additionDiscount;
                nativeTotal += ad.price * ad.quantity * el.quantity;
                nativeDiscount += (ad.price * ad.quantity * el.quantity * discountRatio) / 100;
            });
        });

        return { subTotal, vat, total, discount, tobaccoTax, nativeTotal, nativeDiscount };
    }
}
