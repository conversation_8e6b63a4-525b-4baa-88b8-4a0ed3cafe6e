import useFetch from "../../../../common/asyncController/useFetch";
import useFlatMutate from "../../../../common/asyncController/useFlatMutate";
import AddAndFilterComponent from "../../../../common/components/AddAndFilterComponent";
import StatusComponent from "../../../../common/components/StatusComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { ICategoryModel } from "../../../../common/models/CategoryModel";
import useActions from "../../../../common/redux/data/useActions";
import { CategoriesApiRepo } from "../../../../common/repos/api/CategoriesApiRepo";
import { AdminCategoryDataTableHeaders } from "./AdminCategoriesConstants";
import AdminCategoriesModal from "./modals/AdminCategoriesModal";

const AdminCategoriesPage = () => {
    const actions = useActions();
    const { isSm } = useScreenSize();

    const { data, isLoading, isError, refetch } = useFetch(
        EndPointsEnums.CATEGORIES,
        CategoriesApiRepo.getCategories
    );

    const updateCategory = useFlatMutate(CategoriesApiRepo.updateCategory, {
        updateCached: {
            key: EndPointsEnums.CATEGORIES,
            operation: "update",
            selector: (data: ICategoryModel) => data.id,
        },
    });

    const handleOnActive = (active: boolean, item: ICategoryModel) =>
        updateCategory(item.id, { active });

    const handleOnClick = (isEdit?: boolean, item?: ICategoryModel) => {
        actions.openModal({
            component: <AdminCategoriesModal item={item} />,
            title: isEdit ? TranslateConstants.EDIT : TranslateConstants.ADD,
            size: "md",
            showButtons: false,
        });
    };

    return (
        <>
            <AddAndFilterComponent onAdd={handleOnClick} onReload={refetch} />
            <StatusComponent
                isLoading={isLoading}
                isError={isError}
                isEmpty={!data || data.length === 0}
                height={isSm ? 7.3 : 8}
            >
                <TableComponent
                    headers={AdminCategoryDataTableHeaders}
                    items={data || []}
                    selectors={(item: ICategoryModel) => [item.number, item.name]}
                    imageSelector={(item: ICategoryModel) => item.image}
                    showEditButton={true}
                    onEdit={(item: ICategoryModel) => handleOnClick(true, item)}
                    showActiveButton={true}
                    activeSelector={(item: ICategoryModel) => item.active}
                    onActive={handleOnActive}
                />
            </StatusComponent>
        </>
    );
};

export default AdminCategoriesPage;
