import {
    OrderStatusEnum,
    OrderTypeEnum,
    TableStatusEnum,
} from "../../../../common/enums/DataEnums";
import { OrderHelper } from "../../../../common/helpers/OrderHelper";
import { IPosOrderModel } from "../../../../common/models/PosOrderModel";
import { ITableModel } from "../../../../common/models/TableModel";
import { orderUtils } from "../../../../common/utils/OrderUtils";
import { debug } from "../../../../common/utils/CommonUtils";
import { IPosOrder, IPosReturnedOrder } from "../../interface";
import { PosOrderRepo } from "../../repo/PosOrderRepo";
import { DeliveryAppsHelper } from "../../../../common/helpers/DeliveryAppsHelper";
import { AppLocalDB } from "../../../../common/config/localDB";
import { ReturnedOrderHelper } from "../../../../common/helpers/ReturnedOrderHelper";

export class PosOrderUtils {
    static handleOrderData = async (
        order: IPosOrder | IPosOrderModel,
        cash: number = 0,
        network: number = 0
    ): Promise<IPosOrder> => {
        try {
            if (!order.products.length) throw new Error("No products found");

            const deliverApps = DeliveryAppsHelper.getDeliveryApps();
            const { total, vat, subTotal, discount, tobaccoTax } = orderUtils.getPosOrderData(order);
            let orderNumber = order.orderNumber;
            let invoiceNumber = order.invoiceNumber;
            let status = OrderStatusEnum.IN_PROGRESS;
            let deliveryAppFee = 0;
            let totalDue = 0;

            if (!order.orderNumber) {
                const { count } = await PosOrderRepo.getAndCountOrders();
                orderNumber = (count + 1).toString();
            }

            if (!order.invoiceNumber) {
                invoiceNumber = OrderHelper.getNextInvoiceNumber();
            }

            if (
                order.type === OrderTypeEnum.TAKE_AWAY ||
                order.type === OrderTypeEnum.DELIVERY_APP ||
                order.status === OrderStatusEnum.IN_PROGRESS
            ) {
                status = OrderStatusEnum.COMPLETED;
            }

            if (order.type === OrderTypeEnum.DELIVERY_APP && order.deliveryApp) {
                const percentage = (deliverApps as any)?.[order.deliveryApp];
                deliveryAppFee = total * (percentage / 100);
                totalDue = total - deliveryAppFee;
            }

            return {
                ...order,
                orderId: AppLocalDB.generateId(),
                startTime: order.startTime ?? new Date(),
                endTime: new Date(),
                invoiceNumber,
                orderNumber,
                status,
                total,
                vat,
                subTotal,
                discount,
                tobaccoTax,
                cash,
                network,
                deliveryAppFee,
                totalDue,
            };
        } catch (error) {
            debug(`PosOrderUtils [handleOrderData] Error: ${error}`);
            throw error;
        }
    };

    static handleReturnOrderData = async (
        returnedOrder: IPosReturnedOrder,
        cash: number = 0,
        network: number = 0
    ): Promise<IPosReturnedOrder> => {
        try {
            if (!returnedOrder.products.length) throw new Error("No products found");

            const { total, vat, subTotal, discount, tobaccoTax } = orderUtils.getPosOrderData(returnedOrder as IPosOrder);
            const deliverApps = DeliveryAppsHelper.getDeliveryApps();
            let deliveryAppFee = 0;
            let totalDue = 0;

            if (returnedOrder.type === OrderTypeEnum.DELIVERY_APP && returnedOrder.deliveryApp) {
                const percentage = (deliverApps as any)?.[returnedOrder.deliveryApp];
                deliveryAppFee = total * (percentage / 100);
                totalDue = total - deliveryAppFee;
            }

            return {
                id: AppLocalDB.generateId(),
                orderId: returnedOrder.orderId,
                shiftId: returnedOrder.shiftId,
                type: returnedOrder.type,
                deliveryApp: returnedOrder.deliveryApp,
                products: returnedOrder.products,
                table: returnedOrder.table,
                customer: returnedOrder.customer,
                selectedDiscount: returnedOrder.selectedDiscount,
                invoiceNumber: returnedOrder.invoiceNumber,
                returnedInvoiceNumber: ReturnedOrderHelper.getNextReturnedInvoiceNumber(),
                orderNumber: returnedOrder.orderNumber,
                startTime: new Date(),
                total,
                vat,
                subTotal,
                discount,
                tobaccoTax,
                cash,
                network,
                deliveryAppFee,
                totalDue,
            };
        } catch (error) {
            debug(`PosOrderUtils [handleReturnOrderData] Error: ${error}`);
            throw error;
        }
    };

    static handleTableData = (order: IPosOrderModel): ITableModel | undefined => {
        const table = order.table;
        if (!table) return;

        let tableOrder = undefined;
        let status = TableStatusEnum.AVAILABLE;
        let startTime = undefined;

        if (order.status === OrderStatusEnum.IN_PROGRESS) {
            tableOrder = order;
            status = TableStatusEnum.OCCUPIED;
            startTime = order.startTime;
        }

        return {
            ...table,
            order: tableOrder,
            status,
            startTime,
        };
    };

    static handleProductDifference = (
        oldOrder: IPosOrder,
        newOrder: IPosOrder
    ) => {
        const oldProducts = oldOrder.products;
        const newProducts = newOrder.products;
        const products = newProducts.filter((el) => {
            const oldProduct = oldProducts.find((old) => old.product.id === el.product.id);
            return !oldProduct || oldProduct.quantity !== el.quantity || oldProduct.isDeleted;
        });
        const deletedProducts = oldProducts.filter((el) => {
            return !newProducts.some((item) => item.product.id === el.product.id);
        });
        const updatedProductsQuantity = [...products, ...deletedProducts].map((el) => {
            const oldProduct = oldProducts.find((old) => old.product.id === el.product.id);
            const isDeleted = el.quantity === oldProduct?.quantity;
            const quantity = (oldProduct && !isDeleted) ? el.quantity - oldProduct.quantity : el.quantity;
            return { ...el, quantity, isDeleted };
        });
        return { ...newOrder, products: updatedProductsQuantity };
    };
}
