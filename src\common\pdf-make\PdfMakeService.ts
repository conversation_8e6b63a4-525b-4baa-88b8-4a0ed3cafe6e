import { Content, TDocumentDefinitions } from "pdfmake/interfaces";
import { DateUtils } from "../utils/DateUtils";
import { PdfMakeProps } from "./PdfMakeInterfaces";
import { PdfMakeHelper } from "./PdfMakeHelper";

export class PdfMakeService {
    static defaultProps = async (
        content: Content,
        isPrint = false,
        options: PdfMakeProps = { isLandScape: false, header: undefined, headerType: "normal" },
    ): Promise<TDocumentDefinitions> => {
        content = this.handleTextDirectionForArabic(content);

        return {
            content,
            pageSize: {
                width: 842,  // A4 landscape width (in points)
                height: 595,
            },
            pageOrientation: options.isLandScape ? "landscape" : "portrait",
            defaultStyle: {
                font: "Tajawal",
                alignment: "center",
                color: "#000",
                fontSize: 12,
            },
            styles: {
                tableHeader: {
                    bold: true,
                    fillColor: "#1F618D",
                    color: "#fff",
                    fontSize: 10,
                },
                tableHeaderNormal: {
                    bold: true,
                    fillColor: "#d1d5db",
                    color: "black",
                    fontSize: 12,
                },
                table: {
                    fontSize: 10,
                    fillColor: "#EBF5FB",
                },
                tableNormal: {
                    fontSize: 12,
                }
            },
            pageMargins: [
                10, // left
                options.headerType === "invoice" ? 200 : 80, // top
                options.isLandScape ? isPrint ? 60 : 60 : 5, // right
                isPrint ? options.isLandScape ? 20 : 80 : 20 // bottom
            ],
            header: options.header ? this.handleTextDirectionForArabic(options.header) : undefined,
            footer: (currentPage: number, pageCount: number) => {
                return {
                    columns: [
                        {
                            text: DateUtils.format(Date.now(), "dd-MM-yyyy hh:mm A", true),
                            alignment: "left",
                            style: "small",
                            margin: [10, isPrint ? 5 : 0, 0, 0],
                        },
                        {
                            text: `${currentPage} / ${pageCount}`,
                            alignment: "right",
                            style: "small",
                            margin: [0, isPrint ? 5 : 0, options.isLandScape ? 65 : 10, 0],
                        },
                    ],
                };
            },
        };
    };

    private static handleTextDirectionForArabic = (
        content: Content[] | Content
    ): Content[] => {

        const processText = (c: any) => {
            if (c.text) {
                c.text = PdfMakeHelper.reverseTextIfArabic(c.text);
            } else if (c.columns) {
                c.columns = c.columns.map((item: any) =>
                    item.text || item[0]?.text ? { ...(item[0] || item), text: PdfMakeHelper.reverseTextIfArabic(item.text || item[0].text) } : processText(item)
                );
            } else if (c.stack) {
                c.stack = c.stack.map((item: any) =>
                    item.text || item[0]?.text ? { ...(item[0] || item), text: PdfMakeHelper.reverseTextIfArabic(item.text || item[0].text) } : processText(item)
                );
            } else if (c.table) {
                c.table.body = c.table.body.map((row: any) =>
                    row.map((col: any) => {
                        if (col.text) {
                            return { ...col, text: PdfMakeHelper.reverseTextIfArabic(col.text) };
                        } else if (Array.isArray(col)) {
                            return col.map((subCol: any) =>
                                subCol?.text
                                    ? { ...subCol, text: PdfMakeHelper.reverseTextIfArabic(subCol?.text) }
                                    : subCol
                            );
                        } else {
                            return col;
                        }
                    })
                );
            }
            return c;
        };

        if (Array.isArray(content)) return content.map(processText);
        else return [processText(content)];
    };
}