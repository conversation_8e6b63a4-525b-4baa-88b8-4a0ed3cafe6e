import { useState } from "react";
import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import {
    useGetShiftsSumQuery,
    useGetShiftsQuery,
} from "../../../../common/redux/api/slice";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminShiftsReportTableHeaders } from "./AdminShiftsReportsConstants";
import AdminShiftsReportTotalInfoFeature from "./features/AdminShiftsReportTotalInfoFeature";
import { IShiftModel } from "../../../../common/models/ShiftModel";
import { AdminShiftReportService } from "./ShiftReportService";

const AdminShiftReportPage = () => {
    const { isXs, isSm } = useScreenSize();
    const [dateRange, setDateRange] = useState({
        startTime: DateUtils.getStartOfDayNumber(),
        endTime: DateUtils.getEndOfDayNumber(),
    });

    const {
        data: shiftSum,
        isFetching: shiftSumLoading,
        isError: shiftSumError,
    } = useGetShiftsSumQuery(dateRange, { refetchOnMountOrArgChange: true });

    const {
        data: shifts,
        isFetching: shiftsLoading,
        isError: shiftsError,
    } = useGetShiftsQuery(dateRange, { refetchOnMountOrArgChange: true });

    const onDate = (startDate: Date, endDate: Date) => {
        setDateRange({
            startTime: DateUtils.getStartOfDayNumber(startDate),
            endTime: DateUtils.getEndOfDayNumber(endDate),
        });
    };

    const onDownload = () =>
        AdminShiftReportService.handleDownload(dateRange, shifts, shiftSum);

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                onDate={onDate}
                onDownload={onDownload}
                isDownloadDisabled={!shifts?.length || !shiftSum}
            />
            <AdminShiftsReportTotalInfoFeature shiftSum={shiftSum} />
            <StatusComponent
                isEmpty={!shifts?.length}
                isLoading={shiftSumLoading || shiftsLoading}
                isError={shiftSumError || shiftsError}
                height={isXs ? 14.6 : isSm ? 11.6 : 12.6}
            >
                <TableComponent
                    headers={AdminShiftsReportTableHeaders}
                    items={shifts || []}
                    selectors={(item: IShiftModel) => [
                        item.startAmount.toFixed(2),
                        item.endAmount.toFixed(2),
                        item.totalAmount.toFixed(2),
                        item.cashAmount.toFixed(2),
                        item.networkAmount.toFixed(2),
                        item.additionAmount.toFixed(2),
                        item.shortageAmount.toFixed(2),
                        DateUtils.format(item.startTime),
                    ]}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminShiftReportPage;
