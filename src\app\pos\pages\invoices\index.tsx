import { useState } from "react";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import PosBackButtonComponent from "../../components/PosBackButtonComponent";
import PosInvoicesFeature from "./features/PosInvoicesFeature";
import PosReturnedInvoicesFeature from "./features/PosReturnedInvoiceFeature";
import SelectedButtonsComponent from "../../../../common/components/SelectedButtonsComponent";

const PosInvoicesPage = () => {
    const [returnedView, setReturnedView] = useState(false);

    return (
        <div className="p-4 flex flex-col gap-2">
            <div className="flex justify-between items-center">
                <div className="font-tajawal-bold text-2xl">
                    {TranslateHelper.t(
                        returnedView
                            ? TranslateConstants.RETURNED_INVOICES
                            : TranslateConstants.INVOICES
                    )}
                </div>
                <SelectedButtonsComponent
                    items={[
                        TranslateConstants.INVOICES,
                        TranslateConstants.RETURNED_INVOICES,
                    ]}
                    textSelector={(item: string) => item}
                    defaultSelected={TranslateConstants.INVOICES}
                    onSelect={(item: string) => {
                        setReturnedView(item === TranslateConstants.RETURNED_INVOICES);
                    }}
                    className="w-1/3"
                />
            </div>
            {!returnedView && <PosInvoicesFeature />}
            {returnedView && <PosReturnedInvoicesFeature />}
            <PosBackButtonComponent />
        </div>
    );
};

export default PosInvoicesPage;
