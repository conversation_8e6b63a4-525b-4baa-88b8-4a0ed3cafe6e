import { FC, useMemo } from "react";
import { useTranslate } from "../hooks/useTranslate";
import { useNavigate } from "react-router-dom";

export interface IButtonComponentProps {
    text?: string;
    component?: React.ReactNode;
    subText?: string;
    onClick?: () => void;
    isLoading?: boolean;
    isDisabled?: boolean;
    className?: string;
    type?: "button" | "submit" | "reset";
    bgColor?:
    | "primary"
    | "deepPrimary"
    | "secondary"
    | "warning"
    | "slate"
    | "white"
    | "base-200"
    | "transparent";
    textColor?: "white" | "black" | "primary" | "gray-400" | "slate-600" | "warning";
    borderColor?: "white" | "black" | "primary" | "gray-400" | "slate-600" | "warning";
    borderWidth?: 'border' | 'border-2';
    iconComponent?: React.ReactNode;
    textSize?: "text-sm" | "text-lg" | "text-xl";
    isCentered?: boolean;
    isBorder?: boolean;
    route?: string;
    scaleOnHover?: boolean;
    isHidden?: boolean;
}

const ButtonComponent: FC<IButtonComponentProps> = ({
    text,
    component,
    subText,
    onClick,
    isLoading,
    isDisabled,
    className = "",
    type = "button",
    bgColor = "primary",
    textColor = "white",
    borderColor,
    borderWidth,
    iconComponent,
    textSize = "",
    isCentered = true,
    isBorder = false,
    route,
    scaleOnHover = false,
    isHidden = false
}) => {
    const { translate } = useTranslate();
    const navigate = useNavigate();
    const handleOnClick = (e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        e.stopPropagation();
        if (isDisabled || isLoading) return;
        onClick?.();
        if (route) navigate(route);
    };

    const getBgColor = useMemo(() => {
        switch (bgColor) {
            case "primary":
                return "bg-[#226bb2] hover:bg-[#1B548DFF]";
            case "deepPrimary":
                return "bg-[#1B548DFF]";
            case "secondary":
                return "bg-cyan-700 dark:bg-cyan-700";
            case "warning":
                return "bg-red-700 dark:bg-red-700";
            case "slate":
                return "bg-slate-600 dark:bg-slate-700";
            case "white":
                return "bg-white dark:bg-gray-800";
            case "base-200":
                return "bg-base-200 dark:bg-base-800 hover:bg-base-300 dark:hover:bg-base-700";
            case "transparent":
                return "bg-transparent hover:bg-slate-200 dark:hover:bg-slate-600";
        }
    }, [bgColor]);

    const getTextColor = useMemo(() => {
        switch (textColor) {
            case "white":
                return bgColor === 'white' ? "!text-[#226bb2]" : "!text-white";
            case "black":
                return "!text-black";
            case "primary":
                return "!text-[#226bb2]";
            case "gray-400":
                return "!text-gray-400";
            case "slate-600":
                return "!text-slate-600";
            case "warning":
                return "!text-red-700";
        }
    }, [textColor]);

    const getBorderColor = useMemo(() => {
        switch (borderColor) {
            case "white":
                return "border border-white";
            case "black":
                return "border border-black";
            case "primary":
                return "border border-[#226bb2]";
            case "gray-400":
                return "border border-gray-400";
            case "slate-600":
                return "border border-slate-600";
            case "warning":
                return "border border-red-700";
        }
    }, [borderColor]);

    return (
        <button
            className={
                "rounded text-white font-tajawal-medium p-2 w-full flex items-center transition-all active:scale-95" +
                " " +
                "disabled:cursor-not-allowed disabled:active:scale-100 disabled:transition-none" +
                " " +
                getBgColor +
                " " +
                getTextColor +
                " " +
                getBorderColor +
                " " +
                (scaleOnHover ? "hover:scale-95" : "") +
                " " +
                (borderWidth ?? "") +
                " " +
                (isDisabled ? "bg-slate-400 hover:bg-slate-400 dark:bg-slate-700 !text-white" : "") +
                " " +
                ((subText || !isCentered) ? "justify-between" : "justify-center") +
                " " +
                (isBorder ? " border border-gray-300" : "") +
                " " +
                (isHidden ? "hidden" : "") +
                " " +
                className
            }
            disabled={isDisabled || isLoading}
            onClick={handleOnClick}
            type={type}
        >
            {isLoading && (
                <svg
                    aria-hidden="true"
                    className="w-4 h-4 text-white animate-spin dark:text-gray-600 fill-slate-600"
                    viewBox="0 0 100 101"
                    fill="none"
                >
                    <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="currentColor"
                    />
                    <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentFill"
                    />
                </svg>
            )}
            {!!iconComponent && iconComponent}
            {
                !!text && (
                    <span className={"mx-1 my-auto" + " " + textSize}>
                        {translate(text)}
                    </span>
                )
            }
            {
                !!component && (
                    <span className={"mx-1 my-auto" + " " + textSize}>
                        {component}
                    </span>
                )
            }
            {subText && <span className={"mx-1 my-auto" + " " + textSize}>
                {translate(subText)}
            </span>}
        </button>
    );
};

export default ButtonComponent;
