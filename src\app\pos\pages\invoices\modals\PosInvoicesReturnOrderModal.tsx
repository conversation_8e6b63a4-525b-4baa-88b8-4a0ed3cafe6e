import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { IPosOrderModel } from "../../../../../common/models/PosOrderModel";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ButtonComponent from "../../../../../common/components/ButtonComponent";
import DividerComponent from "../../../../../common/components/DividerComponent";
import ListComponent from "../../../../../common/components/ListComponent";
import PosInvoicesReturnedItemComponent from "../components/PosInvoicesReturnedItemComponent";
import { orderUtils } from "../../../../../common/utils/OrderUtils";
import PriceComponent from "../../../../../common/components/PriceComponent";
import useActions from "../../../../../common/redux/data/useActions";
import PosOrderPaymentModal from "../../../containers/order/modals/PosOrderPaymentModal";
import usePosActions from "../../../redux/usePosActions";
import { OrderTypeEnum } from "../../../../../common/enums/DataEnums";
import { PosInvoicesService } from "../PosInvoicesService";

interface IProps {
    order: IPosOrderModel;
}

const PosInvoicesReturnOrderModal: FC<IProps> = ({ order }) => {
    const actions = useActions();
    const posActions = usePosActions();
    const [orderData, setOrderData] = useState(order);
    const [isReturnTheWholeOrder, setIsReturnTheWholeOrder] = useState(false);

    const handleOrderQuantity = useCallback(
        (quantity?: number) => {
            const products = order.products.map((el) => ({
                ...el,
                quantity: quantity ?? el.quantity,
            }));
            return { ...order, products };
        },
        [order]
    );

    const checkIfAllReturned = useCallback(() => {
        return orderData.products.every((el) => {
            const oldProduct = order.products.find(
                (p) => p.product.id === el.product.id
            );
            return el.quantity === oldProduct?.quantity;
        });
    }, [orderData, order]);

    const { total } = useMemo(() => {
        return orderUtils.getPosOrderData(orderData);
    }, [orderData]);

    useEffect(() => {
        setOrderData(handleOrderQuantity(0));
    }, [order]);

    useEffect(() => {
        setIsReturnTheWholeOrder(checkIfAllReturned());
    }, [orderData]);

    const isProductsQuantityValid = useMemo(() => {
        return orderData.products.some((el) => el.quantity !== 0);
    }, [orderData]);

    const handleOnAdd = (index: number) => {
        const products = [...orderData.products];
        products[index] = {
            ...products[index],
            quantity: products[index].quantity + 1,
        };
        setOrderData({ ...orderData, products });
    };

    const handleOnSubtract = (index: number) => {
        const products = [...orderData.products];
        products[index] = {
            ...products[index],
            quantity: products[index].quantity - 1,
        };
        setOrderData({ ...orderData, products });
    };

    const handleOnRemove = (index: number, oldQuantity: number) => {
        const products = [...orderData.products];
        products[index] = { ...products[index], quantity: oldQuantity };
        setOrderData({ ...orderData, products });
    };

    const handleOnReturnTheWholeOrder = () => {
        setOrderData(handleOrderQuantity(isReturnTheWholeOrder ? 0 : undefined));
        setIsReturnTheWholeOrder(!isReturnTheWholeOrder);
    };

    const handleOnSubmit = () => {
        if (order.type === OrderTypeEnum.DELIVERY_APP) {
            PosInvoicesService.handleOnSubmitReturnOrder(order, orderData, 0, 0, actions, posActions);
            return;
        }

        posActions.setOrder(orderData);
        actions.openModal({
            component: (
                <PosOrderPaymentModal
                    isReturnedOrder={true}
                    oldOrder={order}
                    returnedOrder={orderData}
                />
            ),
            size: "lg",
            title: TranslateConstants.PAY,
            showButtons: false,
        });
    };

    return (
        <>
            <ListComponent padding="0 px-2" calcHeight={30}>
                <>
                    <ButtonComponent
                        text={TranslateConstants.RETURN_THE_WHOLE_ORDER}
                        bgColor={isReturnTheWholeOrder ? "warning" : "transparent"}
                        textColor={isReturnTheWholeOrder ? "white" : "warning"}
                        borderColor="warning"
                        onClick={handleOnReturnTheWholeOrder}
                        className="!h-12"
                    />
                    <DividerComponent />
                    <div className="flex flex-col gap-2">
                        {orderData.products.map((el, index) => {
                            const oldProduct = order.products.find(
                                (p) => p.product.id === el.product.id
                            );
                            return (
                                <PosInvoicesReturnedItemComponent
                                    key={index}
                                    image={el.product.image}
                                    name={el.name}
                                    quantity={el.quantity}
                                    oldQuantity={oldProduct?.quantity || 0}
                                    onAdd={() => handleOnAdd(index)}
                                    onSubtract={() => handleOnSubtract(index)}
                                    onRemove={() =>
                                        handleOnRemove(index, oldProduct?.quantity || 0)
                                    }
                                />
                            );
                        })}
                    </div>
                </>
            </ListComponent>
            <ModalButtonsComponent
                text={TranslateConstants.PAY}
                component={<PriceComponent price={total} color="white" />}
                className="!justify-between"
                isDisabled={!isReturnTheWholeOrder && !isProductsQuantityValid}
                onClick={handleOnSubmit}
            />
        </>
    );
};

export default PosInvoicesReturnOrderModal;
