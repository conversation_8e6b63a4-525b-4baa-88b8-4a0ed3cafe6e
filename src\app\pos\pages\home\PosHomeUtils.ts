import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { DeliveryAppEnum } from "../../../../common/enums/DataEnums";
import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";
import { ShiftHelper } from "../../../../common/helpers/ShiftHelper";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import { IPosShiftModel } from "../../../../common/models/PosShiftModel";
import { IShiftPdfPrinterBodyModel } from "../../../../common/pdf-make/slices/shift/ShiftPdfPrinterModel";
import { DateUtils } from "../../../../common/utils/DateUtils";
import { PosOrderRepo } from "../../repo/PosOrderRepo";
import { PosReturnedOrdersRepo } from "../../repo/PosReturnedOrdersRepo";

export class PosHomeUtils {
    static async getCurrentShiftData(endAmount: number): Promise<IPosShiftModel> {
        const shift = ShiftHelper.get();
        const hasTax = OrganizationHelper.hasVat();
        if (!shift) {
            throw new Error("Shift not found");
        }

        let totalAmount = 0
        let cashAmount = 0
        let networkAmount = 0
        let vatAmount = 0
        let discountAmount = 0
        let tobaccoTaxAmount = 0
        let deliveryAppsAmount = 0
        let totalReturnedAmount = 0

        const {
            count: ordersCount,
            data: orders
        } = await PosOrderRepo.getAndCountOrders();
        const {
            count: returnedOrdersCount,
            data: returnedOrders
        } = await PosReturnedOrdersRepo.getAndCountReturnedOrders();

        orders.forEach((el) => {
            totalAmount += el.total
            cashAmount += el.cash
            networkAmount += el.network
            vatAmount += el.vat
            discountAmount += el.discount
            tobaccoTaxAmount += el.tobaccoTax
            if (el.deliveryApp) deliveryAppsAmount += el.total
        });

        returnedOrders.forEach((el) => {
            totalReturnedAmount += el.total
            cashAmount -= el.cash
            networkAmount -= el.network
            vatAmount -= el.vat
            discountAmount -= el.discount
            tobaccoTaxAmount -= el.tobaccoTax
            if (el.deliveryApp) deliveryAppsAmount -= el.total
        });

        const diffAmount = endAmount - Math.abs(shift.startAmount + cashAmount)
        const additionAmount = diffAmount > 0 ? diffAmount : 0
        const shortageAmount = diffAmount < 0 ? Math.abs(diffAmount) : 0

        if (hasTax) discountAmount *= 1.15

        return {
            ...shift,
            endAmount,
            ordersCount,
            returnedOrdersCount,
            endTime: new Date(),
            totalAmount,
            discountAmount,
            tobaccoTaxAmount,
            cashAmount,
            networkAmount,
            vatAmount,
            additionAmount,
            shortageAmount,
            deliveryAppsAmount,
            totalReturnedAmount,
            totalNetAmount: totalAmount - totalReturnedAmount,
        };
    }

    static async getCurrentSiftDataWithDeliveryApps(endAmount: number): Promise<IShiftPdfPrinterBodyModel> {
        const shift = await this.getCurrentShiftData(endAmount);
        const { data: orders } = await PosOrderRepo.getAndCountOrders();

        let hungerStation = 0;
        let jahez = 0;
        let toYou = 0;
        let theChefz = 0;
        let keeta = 0;
        let ninja = 0;
        let meshwarFood = 0;
        let locate = 0;
        let noonFood = 0;

        orders.forEach((el) => {
            switch (el.deliveryApp) {
                case DeliveryAppEnum.HUNGER_STATION:
                    hungerStation += el.total;
                    break;
                case DeliveryAppEnum.JAHEZ:
                    jahez += el.total;
                    break;
                case DeliveryAppEnum.TO_YOU:
                    toYou += el.total;
                    break;
                case DeliveryAppEnum.THE_CHEFZ:
                    theChefz += el.total;
                    break;
                case DeliveryAppEnum.KEETA:
                    keeta += el.total;
                    break;
                case DeliveryAppEnum.NINJA:
                    ninja += el.total;
                    break;
                case DeliveryAppEnum.MESHWAR_FOOD:
                    meshwarFood += el.total;
                    break;
                case DeliveryAppEnum.LOCATE:
                    locate += el.total;
                    break;
                case DeliveryAppEnum.NOON_FOOD:
                    noonFood += el.total;
                    break;
            }
        });

        const paymentAmounts = [
            { name: TranslateHelper.t(TranslateConstants.HUNGER_STATION), amount: hungerStation, nameEn: "Hunger Station" },
            { name: TranslateHelper.t(TranslateConstants.JAHEZ), amount: jahez, nameEn: "Jahez" },
            { name: TranslateHelper.t(TranslateConstants.TO_YOU), amount: toYou, nameEn: "To You" },
            { name: TranslateHelper.t(TranslateConstants.THE_CHEFZ), amount: theChefz, nameEn: "The Chefz" },
            { name: TranslateHelper.t(TranslateConstants.KEETA), amount: keeta, nameEn: "Keeta" },
            { name: TranslateHelper.t(TranslateConstants.NINJA), amount: ninja, nameEn: "Ninja" },
            { name: TranslateHelper.t(TranslateConstants.MESHWAR_FOOD), amount: meshwarFood, nameEn: "Meshwar Food" },
            { name: TranslateHelper.t(TranslateConstants.LOCATE), amount: locate, nameEn: "Locate" },
            { name: TranslateHelper.t(TranslateConstants.NOON_FOOD), amount: noonFood, nameEn: "Noon Food" },
        ];

        return {
            ...shift,
            startTime: shift.startTime.toString(),
            endTime: DateUtils.toIsoString(shift.endTime),
            paymentAmounts,
        };
    }
}