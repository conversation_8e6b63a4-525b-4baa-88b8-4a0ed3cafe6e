import { DeliveryAppEnum, OrderTypeEnum } from "../enums/DataEnums";

export interface IReturnedOrderProductModel {
    productId: number;
    quantity: number;
    price: number;
    name: string;
    subTotal: number;
    discount: number;
    tobaccoTax: number;
    vat: number;
    total: number;
    startTime: number;
    isSubjectToTobaccoTax: boolean;
}

export interface IReturnedOrderModel {
    id: number;
    orderId: number;
    shiftId: number;
    type: OrderTypeEnum;
    deliveryApp?: DeliveryAppEnum;
    products: IReturnedOrderProductModel[];
    tableId?: number;
    selectedDiscountId?: number;
    invoiceNumber: string;
    returnedInvoiceNumber: string;
    orderNumber: string;
    subTotal: number;
    discount: number;
    tobaccoTax: number;
    vat: number;
    total: number;
    cash: number;
    network: number;
    startTime: number;
    endTime: number;
}
