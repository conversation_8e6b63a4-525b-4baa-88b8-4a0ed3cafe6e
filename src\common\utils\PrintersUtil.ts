import { IPosOrder, IPosReturnedOrder } from "../../app/pos/interface";
import { TranslateConstants } from "../constants/TranslateConstants";
import { KitchenOrderStatusEnum, OrderTypeEnum } from "../enums/DataEnums";
import { OrganizationHelper } from "../helpers/OrganizationHelper";
import { PrintersHelper } from "../helpers/PrintersHelper";
import { QrHelper } from "../helpers/QrHelper";
import { TranslateHelper } from "../helpers/TranslateHelper";
import { IKitchenPrinter } from "../interfaces";
import { IPdfMakePrinterModel } from "../pdf-make/PdfMakeInterfaces";
import { ChequePdfPrinterContent } from "../pdf-make/slices/cheque/chequePdfPrinterContent";
import { InvoicePdfPrinterContent } from "../pdf-make/slices/invoice/InvoicePdfPrinterContent";
import {
    IInvoicePrinterItemModel
} from "../pdf-make/slices/invoice/InvoicePdfPrinterModel";
import { KitchenPdfPrinterContent } from "../pdf-make/slices/kitchen/KitchenPdfPrinterContent";
import { IKitchenPdfPrinterItemModel } from "../pdf-make/slices/kitchen/KitchenPdfPrinterModel";
import { ReturnedInvoicePdfPrinterContent } from "../pdf-make/slices/returnedInvoice/ReturnedInvoicePdfPrinterContent";
import { getOrderType } from "./CommonUtils";
import { DateUtils } from "./DateUtils";
import { fixedNumber } from "./numberUtils";
import { orderUtils } from "./OrderUtils";

export class PrintersUtils {
    static handleInvoiceOrderPrint(order: IPosOrder): IPdfMakePrinterModel {
        const organization = OrganizationHelper.getOrganization();
        const hasVat = OrganizationHelper.hasVat();
        const printerId = PrintersHelper.getDefaultPrinter()?.printer;
        const invoiceTitle = TranslateHelper.t(
            hasVat
                ? TranslateConstants.SIMPLE_VAT_INVOICE
                : TranslateConstants.SIMPLE_INVOICE
        );

        if (!printerId) throw new Error("Printer not found");

        const items: IInvoicePrinterItemModel[] = order.products.map((el) => {
            return {
                product: el.name,
                price: (el.price * el.quantity).toFixed(2),
                quantity: fixedNumber(el.price) + " x " + fixedNumber(el.quantity),
                additions: el.additions?.map((ad) => ({
                    name: ad.name,
                    price: (ad.price * ad.quantity * el.quantity).toFixed(2),
                    quantity:
                        fixedNumber(ad.price) +
                        " x " +
                        fixedNumber(ad.quantity * el.quantity),
                })),
            };
        });

        const { subTotal, vat, total, tobaccoTax, nativeTotal, nativeDiscount } =
            orderUtils.getPosOrderData(order);

        return {
            printerId,
            body: InvoicePdfPrinterContent({
                organizationName: organization?.name || "",
                organizationSubName: organization?.subName || "",
                orderType: getOrderType(order),
                invoiceNumber: order.invoiceNumber || "",
                orderNumber: order.orderNumber || "",
                items,
                invoiceTitle,
                address: organization?.address || "",
                vatNo: organization?.taxNumber || "",
                crNo: organization?.registrationNumber || "",
                phone: organization?.mobile || "",
                qrCode: QrHelper.vatQrData(total, vat),
                subTotal: subTotal.toFixed(2),
                discount: nativeDiscount.toFixed(2),
                vat: vat.toFixed(2),
                total: total.toFixed(2),
                nativeTotal: nativeTotal.toFixed(2),
                cash: order.cash.toFixed(2),
                network: order.network.toFixed(2),
                totalDeliverApp:
                    order.type === OrderTypeEnum.DELIVERY_APP
                        ? order.total.toFixed(2)
                        : "",
                tobaccoTax: tobaccoTax.toFixed(2),
                customerName: order.customer?.name || "",
                customerMobile: order.customer?.mobile || "",
                customerTaxNumber: order.customer?.taxNumber || "",
                customerAddress: order.customer?.address || "",
                footer: organization?.invoiceFooter || "",
                invoiceDate: DateUtils.format(order.startTime || new Date(), "dd-MM-yyyy hh:mm A", true),
            }),
        };
    }

    static handleReturnedInvoiceOrderPrint(
        order: IPosReturnedOrder
    ): IPdfMakePrinterModel {
        const organization = OrganizationHelper.getOrganization();
        const printerId = PrintersHelper.getDefaultPrinter()?.printer;
        const invoiceTitle = TranslateHelper.t(TranslateConstants.RETURNED_INVOICE);

        if (!printerId) throw new Error("Printer not found");

        const { nativeTotal, nativeDiscount } =
            orderUtils.getPosOrderData(order as IPosOrder);

        const items: IInvoicePrinterItemModel[] = order.products.filter((el) => el.quantity !== 0)
            .map((el) => {
                return {
                    product: el.name,
                    price: (el.price * el.quantity).toFixed(2),
                    quantity: fixedNumber(el.price) + " x " + fixedNumber(el.quantity),
                    additions: el.additions?.map((ad) => ({
                        name: ad.name,
                        price: (ad.price * ad.quantity * el.quantity).toFixed(2),
                        quantity:
                            fixedNumber(ad.price) +
                            " x " +
                            fixedNumber(ad.quantity * el.quantity),
                    })),
                };
            });

        return {
            printerId,
            body: ReturnedInvoicePdfPrinterContent({
                organizationName: organization?.name || "",
                organizationSubName: organization?.subName || "",
                invoiceNumber: order.invoiceNumber || "",
                returnedInvoiceNumber: order.returnedInvoiceNumber || "",
                orderNumber: order.orderNumber || "",
                items,
                invoiceTitle,
                address: organization?.address || "",
                vatNo: organization?.taxNumber || "",
                crNo: organization?.registrationNumber || "",
                phone: organization?.mobile || "",
                subTotal: order.subTotal.toFixed(2),
                discount: nativeDiscount.toFixed(2),
                vat: order.vat.toFixed(2),
                total: order.total.toFixed(2),
                nativeTotal: nativeTotal.toFixed(2),
                cash: order.cash.toFixed(2),
                network: order.network.toFixed(2),
                tobaccoTax: order.tobaccoTax.toFixed(2),
                customerName: order.customer?.name || "",
                customerMobile: order.customer?.mobile || "",
                customerTaxNumber: order.customer?.taxNumber || "",
                customerAddress: order.customer?.address || "",
                footer: organization?.invoiceFooter || "",
                invoiceDate: DateUtils.format(order.startTime || new Date(), "dd-MM-yyyy hh:mm A", true),
            }),
        };
    }

    static handleChequeOrderPrint(order: IPosOrder): IPdfMakePrinterModel {
        const organization = OrganizationHelper.getOrganization();
        const printerId = PrintersHelper.getDefaultPrinter()?.printer;
        const chequeTitle = TranslateHelper.t(TranslateConstants.CHEQUE);

        if (!printerId) throw new Error("Printer not found");

        const items: IInvoicePrinterItemModel[] = order.products.map((el) => {
            return {
                product: el.name,
                price: (el.price * el.quantity).toFixed(2),
                quantity: fixedNumber(el.price) + " x " + fixedNumber(el.quantity),
                additions: el.additions?.map((ad) => ({
                    name: ad.name,
                    price: (ad.price * ad.quantity * el.quantity).toFixed(2),
                    quantity:
                        fixedNumber(ad.price) +
                        " x " +
                        fixedNumber(ad.quantity * el.quantity),
                })),
            };
        });

        const { subTotal, vat, total, tobaccoTax, nativeTotal, nativeDiscount } =
            orderUtils.getPosOrderData(order);

        return {
            printerId,
            body: ChequePdfPrinterContent({
                organizationName: organization?.name || "",
                organizationSubName: organization?.subName || "",
                invoiceNumber: order.invoiceNumber || "",
                orderNumber: order.orderNumber || "",
                items,
                invoiceTitle: chequeTitle,
                address: organization?.address || "",
                vatNo: organization?.taxNumber || "",
                crNo: organization?.registrationNumber || "",
                phone: organization?.mobile || "",
                subTotal: subTotal.toFixed(2),
                discount: nativeDiscount.toFixed(2),
                tobaccoTax: tobaccoTax.toFixed(2),
                vat: vat.toFixed(2),
                total: total.toFixed(2),
                nativeTotal: nativeTotal.toFixed(2),
                customerName: order.customer?.name || "",
                customerMobile: order.customer?.mobile || "",
                customerTaxNumber: order.customer?.taxNumber || "",
                customerAddress: order.customer?.address || "",
                invoiceDate: DateUtils.format(order.startTime || new Date(), "dd-MM-yyyy hh:mm A", true),
                footer: organization?.invoiceFooter || "",
            }),
        };
    }

    static handelKitchenOrderPrint(
        order: IPosOrder,
        orderStatus: KitchenOrderStatusEnum
    ): IPdfMakePrinterModel[] {
        const kitchenPrinterItems = this.handleKitchenPrinterItems(order, orderStatus);
        const organization = OrganizationHelper.getOrganization();
        const result: IPdfMakePrinterModel[] = [];

        kitchenPrinterItems.forEach((item) => {
            result.push({
                printerId: item.printerId,
                body: KitchenPdfPrinterContent({
                    items: item.products,
                    organizationName: organization?.name || "",
                    orderNumber: order.orderNumber || "",
                    orderType: TranslateHelper.t(order.deliveryApp ?? order.type),
                    table: order.table?.name || "",
                    orderTitle: TranslateHelper.t(orderStatus),
                }),
            });
        });

        return result;
    }

    private static handleKitchenPrinterItems(
        order: IPosOrder,
        orderStatus: KitchenOrderStatusEnum
    ): IKitchenPrinter[] {
        const result: IKitchenPrinter[] = [];
        const printers = PrintersHelper.getPrinters();

        order.products.forEach((el) => {
            const printer = printers.find(
                (p) => p.categoryId === el.product.categoryId.toString()
            );
            if (!printer?.printer) return;

            const kitchenOrder = result.find(
                (ko) => ko.printerId === printer.printer
            );
            const isDeleted = orderStatus === KitchenOrderStatusEnum.DELETE || el.isDeleted;

            const item: IKitchenPdfPrinterItemModel = {
                product: el.name,
                quantity: (isDeleted ? "-" : "") + fixedNumber(el.quantity).toString(),
                additions: el.additions?.map((ad) => ({
                    name: ad.name,
                    quantity: fixedNumber(ad.quantity).toString(),
                })),
                isDeleted
            };

            if (kitchenOrder) {
                kitchenOrder.products.push(item);
            } else {
                result.push({
                    printerId: printer.printer,
                    products: [item],
                });
            }
            return;
        });

        return result;
    }
}
