import { IAdminPurchaseInvoiceState } from "../../../app/admin/pages/purchaseInvoice/AdminPurchaseInvoiceInterfaces";
import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IPurchaseInvoiceBody } from "../../interfaces/body/PurchaseInvoiceBody";
import { ISplitPaymentState } from "../../modals/SplitPaymentModal";
import { IPurchaseInvoiceModel } from "../../models/PurchaseInvoiceModel";
import { IPurchaseInvoiceSumModel } from "../../models/PurchaseInvoiceSumModel";
import { debug } from "../../utils/CommonUtils";
import { fixedNumber } from "../../utils/numberUtils";

export class PurchaseInvoiceApiRepo {
    static async getPurchaseInvoice(options?: {
        startTime?: number;
        endTime?: number;
        getPurchaseInvoiceProducts?: boolean;
        getSupplier?: boolean;
        supplierId?: number;
        search?: string,
        limit?: number,
    }) {
        try {
            const res = await AxiosHelper.get<IPurchaseInvoiceModel[]>(
                EndPointsEnums.PURCHASE_INVOICES,
                {
                    params: {
                        getPurchaseInvoiceProducts: options?.getPurchaseInvoiceProducts ?? true,
                        getSupplier: options?.getSupplier ?? true,
                        startTime: options?.startTime,
                        endTime: options?.endTime,
                        supplierId: options?.supplierId,
                        search: options?.search,
                        limit: options?.limit,
                    },
                }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PurchaseInvoiceApiRepo [getPurchaseInvoice] Error: `, error);
            throw error;
        }
    }

    static async addPurchaseInvoice(
        body: IAdminPurchaseInvoiceState,
        payment: ISplitPaymentState,
        purchaseOrderId?: number
    ) {
        try {
            const formattedBody: IPurchaseInvoiceBody = {
                ...body,
                supplierId: body.supplier?.id ?? 0,
                purchaseOrderId,
                purchaseInvoiceProducts: body.purchaseInvoiceProducts.map(
                    (rawMaterial) => ({
                        rawMaterialId: rawMaterial.rawMaterial.id,
                        isTaxable: rawMaterial.isTaxable,
                        name: rawMaterial.rawMaterial.name,
                        quantity: fixedNumber(rawMaterial.quantity),
                        price: fixedNumber(rawMaterial.price),
                        discount: fixedNumber(rawMaterial.discount),
                        subTotal: fixedNumber(rawMaterial.subTotal),
                        vat: fixedNumber(rawMaterial.vat),
                        total: fixedNumber(rawMaterial.total),
                    })
                ),
                paymentDate: payment.date,
                cash: payment.cash,
                network: payment.network,
                deferred: payment.deferred,
            };

            delete (formattedBody as any).supplier;
            delete (formattedBody as any).paymentMethod;

            const res = await AxiosHelper.post<IPurchaseInvoiceModel>(
                EndPointsEnums.PURCHASE_INVOICES,
                formattedBody
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PurchaseInvoiceApiRepo [addPurchaseInvoice] Error: `, error);
            throw error;
        }
    }

    static async getSum(startTime: number, endTime: number) {
        try {
            const res = await AxiosHelper.get<IPurchaseInvoiceSumModel>(
                EndPointsEnums.PURCHASE_INVOICES_SUM,
                { params: { startTime, endTime } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PurchaseInvoiceApiRepo [getSum] Error: `, error);
            throw error;
        }
    }

    static async payPurchaseInvoice(
        invoice: IPurchaseInvoiceModel,
        payment: ISplitPaymentState
    ) {
        try {
            const body: Partial<IPurchaseInvoiceBody> = {
                paymentDate: payment.date,
                cash: payment.cash,
                network: payment.network,
                deferred: payment.deferred,
            };
            const res = await AxiosHelper.patch<IPurchaseInvoiceModel>(
                EndPointsEnums.PURCHASE_INVOICES_PAY,
                invoice.id,
                body
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PurchaseInvoiceApiRepo [payPurchaseInvoice] Error: `, error);
            throw error;
        }
    }

    static async getPurchaseInvoiceCount() {
        try {
            const res = await AxiosHelper.get<number>(
                EndPointsEnums.PURCHASE_INVOICES_COUNT
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PurchaseInvoiceApiRepo [getPurchaseInvoiceCount] Error: `, error);
            throw error;
        }
    }
}
