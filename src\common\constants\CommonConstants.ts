import hungerStation from '/images/hungerStation.jpg';
import jahez from '/images/jahez.jpg';
import toYou from '/images/toYou.jpg';
import theChefz from '/images/theChefz.jpg';
import keeta from '/images/keeta.jpg';
import ninja from '/images/ninja.jpg';
import meshwarFood from '/images/meshwarFood.jpg';
import locate from '/images/locate.jpg';
import noonFood from '/images/noonFood.jpg';
import { IDeliveryApp } from '../interfaces';
import { DeliveryAppEnum, PaymentsEnum } from '../enums/DataEnums';
import { TranslateConstants } from './TranslateConstants';

export const AppVersion = "1.0.6";
export const PrinterServerHost = 'http://127.0.0.1:3007';

export const Size = {
    MB: 1024 * 1024,
};

export const TaxPercentage = 0.15;
export const TobaccoTaxPercentage = 2;

export const WhatsAppLink = (message: string) => {
    return `https://api.whatsapp.com/send/?phone=9660552994007&text=${message}&type=phone_number&app_absent=0`;
}

export const deliveryAppsConstant: IDeliveryApp[] = [
    {
        name: DeliveryAppEnum.HUNGER_STATION,
        image: hungerStation
    },
    {
        name: DeliveryAppEnum.JAHEZ,
        image: jahez
    },
    {
        name: DeliveryAppEnum.TO_YOU,
        image: toYou
    },
    {
        name: DeliveryAppEnum.THE_CHEFZ,
        image: theChefz
    },
    {
        name: DeliveryAppEnum.KEETA,
        image: keeta
    },
    {
        name: DeliveryAppEnum.NINJA,
        image: ninja
    },
    {
        name: DeliveryAppEnum.MESHWAR_FOOD,
        image: meshwarFood
    },
    {
        name: DeliveryAppEnum.LOCATE,
        image: locate
    },
    {
        name: DeliveryAppEnum.NOON_FOOD,
        image: noonFood
    },
]

export const PaymentMethodsConstant = [
    {
        name: TranslateConstants.CASH,
        value: PaymentsEnum.CASH,
    },
    {
        name: TranslateConstants.BANK,
        value: PaymentsEnum.BANK,
    },
    {
        name: TranslateConstants.CREDIT,
        value: PaymentsEnum.CREDIT,
    },
];

export const VatTypesConstant = [
    {
        name: TranslateConstants.PRICE_INCLUDING_VAT,
        value: true,
    },
    {
        name: TranslateConstants.PRICE_EXCLUDING_VAT,
        value: false,
    },
];