import { DeliveryAppEnum, OrderTypeEnum } from "../../enums/DataEnums";

export interface IReturnedOrderProductAdditions {
    additionId: number;
    quantity: number;
    price: number;
    name: string;
    subTotal: number;
    discount: number;
    vat: number;
    total: number;
    startTime: number;
}

export interface IReturnedOrderProductBody {
    productId: number;
    quantity: number;
    price: number;
    name: string;
    isSubjectToTobaccoTax: boolean;
    subTotal: number;
    discount: number;
    tobaccoTax: number;
    vat: number;
    total: number;
    startTime: number;
    returnedOrderProductAdditions?: IReturnedOrderProductAdditions[]
}

export interface IReturnedOrderBody {
    orderId: number;
    shiftId: number;
    type: OrderTypeEnum;
    deliveryApp?: DeliveryAppEnum;
    returnedOrderProducts: IReturnedOrderProductBody[];
    tableId?: number;
    customerId?: number;
    selectedDiscountId?: number;
    invoiceNumber: string;
    returnedInvoiceNumber: string;
    orderNumber: string;
    subTotal: number;
    discount: number;
    vat: number;
    tobaccoTax: number;
    total: number;
    deliveryAppFee: number;
    totalDue: number;
    cash: number;
    network: number;
    startTime: number;
}