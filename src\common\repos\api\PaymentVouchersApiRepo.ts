import { IAdminPaymentVoucherInputs } from "../../../app/admin/pages/paymentVoucher/AdminPaymentVoucherInterface";
import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IPaymentVoucherBody } from "../../interfaces/body/PaymentVoucherBody";
import { IPaymentVoucherModel } from "../../models/PaymentVoucherModel";
import { IPaymentVoucherSumModel } from "../../models/PaymentVoucherSumModel";
import { debug } from "../../utils/CommonUtils";

export class PaymentVouchersApiRepo {
    static async getPaymentVouchers(options?: {
        startTime?: number;
        endTime?: number;
    }) {
        try {
            const res = await AxiosHelper.get<IPaymentVoucherModel[]>(
                EndPointsEnums.PAYMENT_VOUCHERS,
                { params: { startTime: options?.startTime, endTime: options?.endTime } }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PaymentVouchersApiRepo [getPaymentVouchers] Error: `, error);
            throw error;
        }
    }

    static async addPaymentVoucher(body: IAdminPaymentVoucherInputs) {
        try {
            const formattedBody: IPaymentVoucherBody = body;
            const res = await AxiosHelper.post<IPaymentVoucherModel>(
                EndPointsEnums.PAYMENT_VOUCHERS,
                formattedBody
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PaymentVouchersApiRepo [addPaymentVoucher] Error: `, error);
            throw error;
        }
    }

    static async updatePaymentVoucher(id: number, body: IAdminPaymentVoucherInputs) {
        try {
            const formattedBody: IPaymentVoucherBody = body;
            const res = await AxiosHelper.patch<IPaymentVoucherModel>(
                EndPointsEnums.PAYMENT_VOUCHERS,
                id,
                formattedBody
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PaymentVouchersApiRepo [updatePaymentVoucher] Error: `, error);
            throw error;
        }
    }

    static async getSum(startTime: number, endTime: number) {
        try {
            const res = await AxiosHelper.get<IPaymentVoucherSumModel>(
                EndPointsEnums.PAYMENT_VOUCHERS_SUM,
                { params: { startTime, endTime } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`PaymentVouchersApiRepo [getSum] Error: `, error);
            throw error;
        }
    }
}
