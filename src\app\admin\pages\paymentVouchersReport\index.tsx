import { useState } from "react";
import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminPaymentVouchersReportTableHeaders } from "./AdminPaymentVouchersConstants";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import AdminPaymentVouchersReportTotalInfoFeature from "./features/AdminPaymentVouchersReportTotalInfoFeature";
import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";
import { AdminPaymentVouchersReportService } from "./PaymentVouchersReportService";
import useFetch from "../../../../common/asyncController/useFetch";
import { PaymentVouchersApiRepo } from "../../../../common/repos/api/PaymentVouchersApiRepo";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { IPaymentVoucherModel } from "../../../../common/models/PaymentVoucherModel";

const AdminPaymentVouchersReportPage = () => {
    const { isXs, isSm } = useScreenSize();
    const [dateRange, setDateRange] = useState({
        startTime: DateUtils.getStartOfDayNumber(),
        endTime: DateUtils.getEndOfDayNumber(),
    });

    const {
        data: paymentVouchersSum,
        isLoading: paymentVouchersSumLoading,
        isError: paymentVouchersSumError,
        refetch: refetchPaymentVouchersSum,
    } = useFetch(
        "report-" + EndPointsEnums.PAYMENT_VOUCHERS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => PaymentVouchersApiRepo.getSum(startTime, endTime),
        { autoFetchOnMount: true }
    );

    const {
        data: paymentVouchers,
        isLoading: paymentVouchersLoading,
        isError: paymentVouchersError,
        refetch: refetchPaymentVouchers,
    } = useFetch(
        "report-" + EndPointsEnums.PAYMENT_VOUCHERS,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => PaymentVouchersApiRepo.getPaymentVouchers({ startTime, endTime }),
        { autoFetchOnMount: true }
    );

    const onDate = (startDate: Date, endDate: Date) => {
        const start = DateUtils.getStartOfDayNumber(startDate);
        const end = DateUtils.getEndOfDayNumber(endDate);
        setDateRange({ startTime: start, endTime: end });
        refetchPaymentVouchersSum(start, end);
        refetchPaymentVouchers(start, end);
    };

    // const onDownload = () =>
    //     AdminPaymentVouchersReportService.handleDownload(
    //         dateRange,
    //         orders,
    //         orderSum
    //     );

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                onDate={onDate}
                // onDownload={onDownload}
                isDownloadDisabled={!paymentVouchers?.length || !paymentVouchersSum}
            />
            <AdminPaymentVouchersReportTotalInfoFeature
                paymentVoucherSum={paymentVouchersSum}
            />
            <StatusComponent
                isEmpty={!paymentVouchers?.length}
                isLoading={paymentVouchersSumLoading || paymentVouchersLoading}
                isError={paymentVouchersSumError || paymentVouchersError}
                height={isXs ? 14.6 : isSm ? 11.6 : 12.6}
            >
                <TableComponent
                    headers={AdminPaymentVouchersReportTableHeaders}
                    items={paymentVouchers || []}
                    selectors={(item: IPaymentVoucherModel) => [
                        DateUtils.format(item.date),
                        item.number,
                        item.cash,
                        item.network,
                        item.note,
                    ]}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminPaymentVouchersReportPage;
