import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminDeliveryAppsReportTableHeaders } from "./AdminDeliveryAppsReportsConstants";
import { IOrderModel } from "../../../../common/models/OrderModel";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import AdminDeliveryAppsReportTotalInfoFeature from "./features/AdminDeliveryAppsReportTotalInfoFeature";
import useFetch from "../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { OrdersApiRepo } from "../../../../common/repos/api/OrdersApiRepo";
import { DeliveryAppsApiRepo } from "../../../../common/repos/api/DeliveryAppsApiRepo";
import { IDeliveryApp } from "../../../../common/interfaces";
import { DeliveryAppsHelper } from "../../../../common/helpers/DeliveryAppsHelper";
import { useMemo, useState } from "react";
import { AdminDeliveryAppsReportService } from "./DeliveryAppsReportService";
import { ReturnedOrdersApiRepo } from "../../../../common/repos/api/ReturnedOrdersApiRepo";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";

const AdminDeliveryAppsReportPage = () => {
    const { isXs, isSm } = useScreenSize();
    const [currentDateRange, setCurrentDateRange] = useState({
        startTime: DateUtils.getStartOfDayNumber(),
        endTime: DateUtils.getEndOfDayNumber(),
    });
    const [currentDeliveryApp, setCurrentDeliveryApp] = useState<IDeliveryApp | undefined>(undefined);

    const {
        data: deliverApps,
        isLoading: deliverAppsLoading,
        isError: deliverAppsError,
    } = useFetch(
        EndPointsEnums.DELIVERY_APPS, DeliveryAppsApiRepo.getDeliveryApps,
        {
            resetOnUnmount: true,
            onFirstSuccess({ data }) {
                const now = new Date();
                const activeApps = DeliveryAppsHelper.getActiveDeliveryApps(data);
                activeApps?.[0] && onDate(now, now, activeApps?.[0]);
            },
        }
    );

    const {
        data: orderSum,
        isLoading: orderSumLoading,
        isError: orderSumError,
        refetch: refetchOrderSum,
    } = useFetch(
        "report-deliveryApps-" + EndPointsEnums.ORDERS_SUM,
        (startTime, endTime, deliveryApp) => OrdersApiRepo.getSum(
            startTime,
            endTime,
            undefined,
            deliveryApp?.name
        ),
        { autoFetchIfEmpty: false }
    );

    const {
        data: orders,
        isLoading: ordersLoading,
        isError: ordersError,
        refetch: refetchOrders,
    } = useFetch(
        "report-deliveryApps-" + EndPointsEnums.ORDERS,
        (startTime, endTime, deliveryApp) => OrdersApiRepo.getOrders(
            startTime,
            endTime,
            undefined,
            undefined,
            deliveryApp?.name
        ),
        { autoFetchIfEmpty: false }
    );

    const {
        data: returnedOrderSum,
        isLoading: returnedOrderSumLoading,
        isError: returnedOrderSumError,
        refetch: refetchReturnedOrderSum,
    } = useFetch(
        "report-deliveryApps-" + EndPointsEnums.RETURNED_ORDERS_SUM,
        (startTime, endTime, deliveryApp) => ReturnedOrdersApiRepo.getReturnedOrdersSum(
            startTime,
            endTime,
            undefined,
            deliveryApp?.name
        ),
        { autoFetchIfEmpty: false }
    );

    const {
        data: returnedOrders,
        isLoading: returnedOrdersLoading,
        isError: returnedOrdersError,
        refetch: refetchReturnedOrders,
    } = useFetch(
        "report-deliveryApps-" + EndPointsEnums.RETURNED_ORDERS,
        (startTime, endTime, deliveryApp) => ReturnedOrdersApiRepo.getReturnedOrders(
            startTime,
            endTime,
            undefined,
            undefined,
            deliveryApp?.name
        ),
        { autoFetchIfEmpty: false }
    );

    const onDate = (
        startDate: Date = new Date(),
        endDate: Date = new Date(),
        deliveryApp?: IDeliveryApp
    ) => {
        const start = DateUtils.getStartOfDayNumber(startDate);
        const end = DateUtils.getEndOfDayNumber(endDate);
        setCurrentDateRange({ startTime: start, endTime: end });
        setCurrentDeliveryApp(deliveryApp);
        refetchOrderSum(start, end, deliveryApp);
        refetchOrders(start, end, deliveryApp);
        refetchReturnedOrderSum(start, end, deliveryApp);
        refetchReturnedOrders(start, end, deliveryApp);
    };

    const onDownload = () => {
        currentDeliveryApp && AdminDeliveryAppsReportService.handleDownload(
            currentDateRange,
            orders,
            orderSum,
            currentDeliveryApp
        );
    };

    const deliverAppsItems = useMemo(() => {
        const activeApps = deliverApps ? DeliveryAppsHelper.getActiveDeliveryApps(deliverApps) : [];
        setCurrentDeliveryApp(activeApps?.[0]);
        return activeApps;
    }, [deliverApps]);

    const data = useMemo(() => {
        return AdminDeliveryAppsReportService.handleData(
            orders,
            returnedOrders
        );
    }, [orders, returnedOrders]);

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                showSearch={true}
                onDate={onDate}
                items={deliverAppsItems}
                isSearchLoading={deliverAppsLoading}
                isSearchError={deliverAppsError}
                titleSelector={(item) => TranslateHelper.t(item.name)}
                defaultValue={TranslateHelper.t(deliverAppsItems?.[0]?.name)}
                defaultItem={deliverAppsItems?.[0]}
                isSearchable={false}
                onDownload={onDownload}
                isDownloadDisabled={!orders?.length || !orderSum}
            />
            <AdminDeliveryAppsReportTotalInfoFeature
                orderSum={orderSum}
                returnedOrderSum={returnedOrderSum}
            />
            <StatusComponent
                isEmpty={!data?.length}
                isLoading={
                    orderSumLoading ||
                    ordersLoading ||
                    returnedOrderSumLoading ||
                    returnedOrdersLoading
                }
                isError={
                    orderSumError ||
                    ordersError ||
                    returnedOrderSumError ||
                    returnedOrdersError
                }
                height={isXs ? 17.5 : isSm ? 14.6 : 15.6}
            >
                <TableComponent
                    headers={AdminDeliveryAppsReportTableHeaders}
                    items={data || []}
                    selectors={(item: (IOrderModel & { isReturned: boolean, customNumber: string })) => [
                        item.customNumber,
                        TranslateHelper.t(item.isReturned ? TranslateConstants.RETURNED : TranslateConstants.SELL),
                        item.subTotal.toFixed(2),
                        item.discount.toFixed(2),
                        item.vat.toFixed(2),
                        item.total.toFixed(2),
                        item.deliveryAppFee.toFixed(2),
                        item.totalDue.toFixed(2),
                    ]}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminDeliveryAppsReportPage;
