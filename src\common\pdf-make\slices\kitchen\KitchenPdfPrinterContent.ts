import { Content } from 'pdfmake/interfaces';
import { IKitchenPdfPrinterBodyModel } from './KitchenPdfPrinterModel';
import { PdfMakeHelper } from '../../PdfMakeHelper';
import { DateUtils } from '../../../utils/DateUtils';

export const KitchenPdfPrinterContent = (
    invoice: IKitchenPdfPrinterBodyModel,
): Content[] => {
    return [
        {
            image: './images/logo.jpg',
            width: 150,
            height: 100,
        },
        ...PdfMakeHelper.optionalDocItem(!!invoice.organizationName, [
            {
                text: invoice.organizationName!,
                bold: true,
                fontSize: 15,
            },
        ]),
        PdfMakeHelper.printDivider(),
        ...PdfMakeHelper.optionalDocItem(!!invoice.orderNumber, [
            PdfMakeHelper.textWithBorder(`Order # ${invoice.orderNumber}`),
        ]),
        ...PdfMakeHelper.optionalDocItem(!!invoice.orderTitle, [
            {
                text: invoice.orderTitle!,
                bold: true,
                fontSize: 14,
                marginTop: 3,
            },
        ]),
        PdfMakeHelper.printDivider(),
        {
            columns: [
                {
                    text: DateUtils.format(Date.now(), "dd-MM-yyyy hh:mm A", true),
                    alignment: 'left',
                    marginBottom: 5,
                },
                {
                    text: 'التاريخ:',
                    alignment: 'right',
                },
            ],
        },
        ...PdfMakeHelper.optionalDocItem(!!invoice.orderType, [
            {
                columns: [
                    {
                        text: invoice.orderType!,
                        alignment: 'left',
                        marginBottom: 5,
                    },
                    {
                        text: 'نوع الطلب:',
                        alignment: 'right',
                    },
                ],
            },
        ]),
        ...PdfMakeHelper.optionalDocItem(!!invoice.table, [
            {
                columns: [
                    {
                        text: invoice.table || '',
                        alignment: 'left',
                    },
                    {
                        text: 'الطاولة:',
                        alignment: 'right',
                    },
                ],
            },
        ]),
        PdfMakeHelper.printDivider(),
        {
            table: {
                headerRows: 1,
                widths: ['auto', '*'],
                body: [
                    [
                        { text: 'الكمية', bold: true },
                        { text: 'المنتج', bold: true, alignment: 'right' },
                    ],
                    ...invoice.items.map((product) => {
                        const additions = product.additions?.map((a, index) => a.quantity + ' * ' + a.name + (index < (product.additions?.length || 0) - 1 ? " ," : "")).join(' ');

                        return [
                            { text: product.quantity, fontSize: 8 },
                            [
                                {
                                    text: product.product,
                                    alignment: 'right',
                                    fontSize: 8,
                                    decoration: product.isDeleted ? 'lineThrough' : undefined,
                                },
                                !!product.additions?.length
                                    ? {
                                        text: ' ) ' + additions + ' ( ',
                                        alignment: 'right',
                                        fontSize: 7,
                                        decoration: product.isDeleted ? 'lineThrough' : undefined
                                    }
                                    : null,
                            ],
                        ];
                    }),
                ],
            },
        },
        ...PdfMakeHelper.optionalDocItem(!!invoice.note, [
            PdfMakeHelper.printDivider(),
            {
                text: `ملاحظة: ${invoice.note}`,
                alignment: 'right',
                fontSize: 10,
            },
        ]),
    ];
};
