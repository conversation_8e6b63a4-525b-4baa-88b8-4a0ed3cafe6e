import { DeliveryAppEnum, OrderStatusEnum, OrderTypeEnum, ProductSizeTypeEnum } from "../../../common/enums/DataEnums";
import { IAdditionModel } from "../../../common/models/AdditionsModel";
import { ICustomerModel } from "../../../common/models/CustomerModel";
import { IDiscountModel } from "../../../common/models/DiscountModel";
import { IOrganizationUpdatesCountModel } from "../../../common/models/OrganizationUpdatesCountModel";
import { IProductModel, IProductSizeModel } from "../../../common/models/ProductModel";
import { ITableModel } from "../../../common/models/TableModel";

export interface IPosOrderProductAddition {
    addition: IAdditionModel;
    quantity: number;
    price: number;
    name: string;
    subTotal?: number;
    discount?: number;
    vat?: number;
    total?: number;
    startTime?: number;
}

export interface IPosOrderProduct {
    product: IProductModel;
    size?: IProductSizeModel;
    additions?: IPosOrderProductAddition[];
    productSizeType: ProductSizeTypeEnum;
    quantity: number;
    price: number;
    name: string;
    isSubjectToTobaccoTax?: boolean;
    subTotal?: number;
    discount?: number;
    tobaccoTax?: number;
    vat?: number;
    total?: number;
    startTime?: number;
    isDeleted?: boolean
}

export interface IPosOrder {
    id: number;
    orderId: number;
    shiftId: number;
    type: OrderTypeEnum;
    deliveryApp?: DeliveryAppEnum;
    products: IPosOrderProduct[];
    table?: ITableModel;
    customer?: ICustomerModel;
    selectedDiscount?: IDiscountModel;
    status: OrderStatusEnum;
    invoiceNumber?: string;
    orderNumber?: string;
    subTotal: number;
    discount: number;
    vat: number;
    tobaccoTax: number;
    total: number;
    deliveryAppFee: number;
    totalDue: number;
    cash: number;
    network: number;
    startTime?: Date;
    endTime?: Date;
}

export interface IPosReturnedOrder extends Omit<IPosOrder, "status" | "endTime"> {
    returnedInvoiceNumber?: string;
}

export type PosHomeViewType = 'categories' | 'products' | 'tables' | 'delivery_apps';

export interface IUpdatesMetaData extends IOrganizationUpdatesCountModel {
    lastPosUpdateAt: Date;
}
