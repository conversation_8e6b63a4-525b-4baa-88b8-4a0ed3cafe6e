import { BaseModel } from ".";

export interface IShiftModel extends BaseModel { 
    number: number;
    shiftId: string;
    startTime: string;
    endTime: string;
    startAmount: number;
    endAmount: number;
    ordersCount: number;
    returnedOrdersCount: number;
    totalAmount: number;
    totalReturnedAmount: number;
    totalNetAmount: number; // the totalAmount - totalReturnedAmount
    discountAmount: number;
    tobaccoTaxAmount: number;
    vatAmount: number;
    additionAmount: number;
    shortageAmount: number;
    cashAmount: number;
    networkAmount: number;
    deliveryAppsAmount: number;
    organizationId: number;
}