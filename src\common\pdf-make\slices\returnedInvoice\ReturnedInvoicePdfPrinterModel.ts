interface IReturnedInvoiceAdditionModel {
    name: string;
    quantity: string;
    price: string;
}

export interface IReturnedInvoicePrinterItemModel {
    quantity: string;
    product: string;
    price: string;
    additions?: IReturnedInvoiceAdditionModel[];
}

export interface IReturnedInvoicePdfPrinterBodyModel {
    organizationName: string;
    organizationSubName: string;
    invoiceNumber: string;
    returnedInvoiceNumber: string;
    orderNumber: string;
    items: IReturnedInvoicePrinterItemModel[];
    address?: string;
    vatNo?: string;
    crNo?: string;
    phone?: string;
    invoiceTitle: string;
    subTotal: string;
    discount: string;
    tobaccoTax?: string;
    vat: string;
    total: string;
    nativeTotal: string;
    cash: string;
    network: string;
    footer?: string;
    customerName?: string;
    customerMobile?: string;
    customerAddress?: string;
    customerTaxNumber?: string;
    invoiceDate: string;
}