import { NavLink } from "react-router-dom";
import { memo, useState } from "react";
import logo from "/logo.svg";
import { DrawerRoutesConstants } from "../../../common/constants/DrawerRoutesConstants";
import SideBarSubMenuComponent from "../../../common/components/SideBarSubMenuComponent";
import { TranslateConstants } from "../../../common/constants/TranslateConstants";
import { AppVersion } from "../../../common/constants/CommonConstants";
import { useTranslate } from "../../../common/hooks/useTranslate";
import { HiOutlineXMark } from "react-icons/hi2";

const AdminDrawerComponent = () => {
    const { translate, isRtl, isArabic } = useTranslate();
    const [expandedMenuIndex, setExpandedMenuIndex] = useState(-1);

    const close = () => {
        document.getElementById("left-sidebar-drawer")?.click();
    };

    return (
        <ul className="menu w-60 dark:bg-base-100 bg-[#226bb2] border dark:border-none text-white font-semibold">
            <button
                className={
                    "btn btn-ghost border hover:bg-white dark:hover:bg-base-100 hover:border-black border-black dark:border-white btn-circle z-50 top-2 absolute lg:hidden p-0"
                    + " " +
                    (isRtl ? "left-0 ml-2" : "right-0 mr-2")
                }
                onClick={() => close()}
            >
                <HiOutlineXMark className="h-5 inline-block w-5 text-black dark:text-white" />
            </button>
            <li className="mb-2 font-semibold text-lg dark:bg-transparent dark:text-white bg-white text-black">
                <a className="bg-inherit active:text-inherit cursor-default px-1 pl-2 gap-2">
                    <img className="mask mask-squircle w-10" src={logo} />
                    {translate(isArabic ? TranslateConstants.APP_NAME : TranslateConstants.APP_NAME_EN)}
                </a>
            </li>
            {DrawerRoutesConstants().map((route, index) => {
                if (route.submenu) {
                    if (route.submenu.length === 0) return null;
                    return (
                        <li key={index}>
                            <SideBarSubMenuComponent
                                {...route}
                                isMenuExpanded={expandedMenuIndex === index}
                                index={index}
                                setExpandedMenuIndex={(i) => setExpandedMenuIndex(i)}
                                onClick={() => {
                                    setExpandedMenuIndex(
                                        expandedMenuIndex === index ? -1 : index
                                    );
                                }}
                                onNavClick={() => close()}
                            />
                        </li>
                    );
                }
                else {
                    return (
                        <li key={index} className="text-sm">
                            <NavLink
                                end
                                to={route.path}
                                className={({ isActive }) => {
                                    return isActive
                                        ? "dark:bg-[#226bb2] dark:!text-white bg-white text-black active:!text-black active:bg-white border-y border-[#226bb2] "
                                        : "active:bg-inherit active:dark:text-white active:text-inherit hover:bg-white hover:text-black dark:hover:text-white dark:hover:bg-[#226bb2]";
                                }}
                                onClick={() => {
                                    setExpandedMenuIndex(-1);
                                    close();
                                }}
                            >
                                {translate(route.name)}
                            </NavLink>
                        </li>
                    );
                }
            })}
            <li className="mt-auto">
                <div className="bg-inherit active:text-inherit flex justify-center">
                    <span className="text-sm">v {AppVersion}</span>
                </div>
            </li>
        </ul>
    );
};

export default memo(AdminDrawerComponent);
