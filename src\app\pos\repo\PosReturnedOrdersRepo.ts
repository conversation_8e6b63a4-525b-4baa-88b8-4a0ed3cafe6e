import { APP_LOCAL_DB_COLLECTIONS, AppLocalDB } from "../../../common/config/localDB";
import { EndPointsEnums } from "../../../common/enums/EndPointsEnums";
import { AxiosHelper } from "../../../common/helpers/AxiosHelper";
import { ShiftHelper } from "../../../common/helpers/ShiftHelper";
import { IReturnedOrderBody } from "../../../common/interfaces/body/ReturnedOrderBody";
import { ILocalDBGetOptionsProperties, IOperator } from "../../../common/LocalDB/LocalDBInterface";
import { IPosOrderModel } from "../../../common/models/PosOrderModel";
import { IPosReturnedOrderModel } from "../../../common/models/PosReturnedOrderModel";
import { debug } from "../../../common/utils/CommonUtils";
import { orderUtils } from "../../../common/utils/OrderUtils";
import { IPosReturnedOrder } from "../interface";

export class PosReturnedOrdersRepo {
    static async addReturnedOrder(returnedOrder: IPosReturnedOrder) {
        try {
            const shift = ShiftHelper.get();
            if (!shift) throw new Error("Shift not found");

            const body: IPosReturnedOrderModel = {
                ...returnedOrder,
                id: AppLocalDB.generateId(),
                products: returnedOrder.products.map((product) => {
                    const { subTotal, vat, total, discount, tobaccoTax } = orderUtils.getPosProductAmounts(
                        product.price,
                        product.quantity,
                        returnedOrder.selectedDiscount?.amount,
                        product.isSubjectToTobaccoTax
                    );
                    return {
                        ...product,
                        additions: product.additions?.map((ad) => {
                            const additionTotalPrice = ad.price * ad.quantity;
                            const { subTotal, vat, total, discount } = orderUtils.getAmounts(
                                additionTotalPrice,
                                returnedOrder.selectedDiscount?.amount
                            );
                            return { ...ad, subTotal, discount, vat, total, startTime: ad.startTime ?? new Date().getTime() };
                        }),
                        subTotal,
                        discount,
                        tobaccoTax,
                        vat,
                        total,
                        startTime: returnedOrder.startTime?.getTime(),
                    };
                }),
                deleted: false,
                createdAt: new Date(),
                updatedAt: new Date(),
            }

            if (!body.products.length) throw new Error("No products found");
            await AppLocalDB.add(APP_LOCAL_DB_COLLECTIONS.RETURNED_ORDERS, body);
        } catch (error) {
            debug(`PosReturnedOrdersRepo [addReturnedOrder] Error: `, error);
            throw error;
        }
    }

    static getAndCountReturnedOrders = async (
        where?: [keyof IPosReturnedOrderModel, IOperator, any]
    ) => {
        try {
            const shift = ShiftHelper.get();
            if (!shift) {
                return { data: [], count: 0 };
            }

            const orderWere: [keyof IPosReturnedOrderModel, IOperator, any][] = [
                [`shiftId`, "==", shift.shiftId],
            ];
            if (where) orderWere.push(where);

            const res = await AppLocalDB.getAndCount<IPosReturnedOrderModel>(
                APP_LOCAL_DB_COLLECTIONS.RETURNED_ORDERS,
                {
                    where: [orderWere],
                }
            );
            return res;
        } catch (error) {
            debug(`PosReturnedOrdersRepo [getAndCountReturnedOrders] Error: ${error}`);
            throw error;
        }
    };

    static async getShiftReturnedOrders() {
        try {
            const shift = ShiftHelper.get();
            if (!shift) return [];

            const res = await AppLocalDB.get<IPosReturnedOrderModel>(
                APP_LOCAL_DB_COLLECTIONS.RETURNED_ORDERS,
                {
                    orderBy: ["returnedInvoiceNumber", "desc"],
                    where: [[[`shiftId`, "===", shift.shiftId]]],
                }
            );
            return res;
        } catch (error) {
            debug(`PosReturnedOrdersRepo [getShiftReturnedOrders] Error: `, error);
            throw error;
        }
    }

    static async uploadReturnedOrders(body: IReturnedOrderBody[]) {
        try {
            const res = await AxiosHelper.post(EndPointsEnums.RETURNED_ORDERS, body);
            if (!res.success) throw new Error(res.message);
        } catch (error) {
            debug(`PosReturnedOrdersRepo [uploadReturnedOrders] Error: `, error);
            throw error;
        }
    }

    static async returnedOrdersCount() {
        try {
            return await AppLocalDB.count(APP_LOCAL_DB_COLLECTIONS.RETURNED_ORDERS);
        } catch (error) {
            debug(`PosReturnedOrdersRepo [returnedOrdersCount] Error: `, error);
            throw error;
        }
    }

    static async getReturnedOrders(
        options?: ILocalDBGetOptionsProperties<IPosReturnedOrderModel>
    ) {
        try {
            return await AppLocalDB.get<IPosReturnedOrderModel>(
                APP_LOCAL_DB_COLLECTIONS.RETURNED_ORDERS,
                options
            );
        } catch (error) {
            debug(`PosReturnedOrderRepo [getReturnedOrders] Error: `, error);
            throw error;
        }
    }

    static async deleteAllReturnedOrders() {
        try {
            await AppLocalDB.deleteAll(APP_LOCAL_DB_COLLECTIONS.RETURNED_ORDERS);
        } catch (error) {
            debug(`PosReturnedOrdersRepo [deleteAllReturnedOrders] Error: `, error);
            throw error;
        }
    }

    static async getReturnedOrdersCount() {
        try {
            const res = await AxiosHelper.get<number>(
                EndPointsEnums.RETURNED_ORDERS_COUNT
            );

            if (!res.success) throw new Error(res.message);

            return res.data || 0;
        } catch (error) {
            debug(`PosReturnedOrdersRepo [getReturnedOrdersCount] Error: `, error);
            throw error;
        }
    }

    static async getRelatedOrder(
        returnedOrder: IPosReturnedOrderModel
    ): Promise<IPosOrderModel | undefined> {
        try {
            const res = await AppLocalDB.getOne<IPosOrderModel>(
                APP_LOCAL_DB_COLLECTIONS.ORDERS,
                ["orderId", "==", returnedOrder.orderId]
            );

            return res;
        } catch (error) {
            debug(`PosReturnedOrdersRepo [getRelatedOrder] Error: `, error);
            throw error;
        }
    }
}