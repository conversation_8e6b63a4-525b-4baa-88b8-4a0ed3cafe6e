import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";

export const AdminCustomersReportTableHeaders = () => {
    const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();

    return [
        TranslateConstants.INVOICE_NUMBER,
        TranslateConstants.INVOICE_TYPE,
        TranslateConstants.ORDER_TYPE,
        TranslateConstants.SUB_TOTAL,
        TranslateConstants.THE_DISCOUNT,
        ...(hasTobaccoTax ? [TranslateConstants.TOBACCO_TAX] : []),
        TranslateConstants.TAX,
        TranslateConstants.TOTAL,
        TranslateConstants.CASH,
        TranslateConstants.NETWORK,
    ]
}
