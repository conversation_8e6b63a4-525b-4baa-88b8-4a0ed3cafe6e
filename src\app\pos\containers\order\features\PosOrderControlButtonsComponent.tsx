import { PiChefHat } from "react-icons/pi";
import { TiPrinter } from "react-icons/ti";
import { FC, useMemo } from "react";
import IconButtonComponent from "../../../../../common/components/IconButtonComponent";
import useActions from "../../../../../common/redux/data/useActions";
import { IPosOrder } from "../../../interface";
import usePosActions from "../../../redux/usePosActions";
import {
    OrderStatusEnum,
    OrderTypeEnum,
} from "../../../../../common/enums/DataEnums";
import { PosOrderService } from "../PosOrderService";
import { IPosOrderModel } from "../../../../../common/models/PosOrderModel";
import { RiDiscountPercentLine } from "react-icons/ri";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import PosOrderDiscountModal from "../modals/PosOrderDiscountModal";

interface IProps {
    order: IPosOrder;
    orderDiff: IPosOrder | IPosOrderModel;
    isProductsEqual: boolean;
}

const PosOrderControlButtonsComponent: FC<IProps> = ({
    order,
    orderDiff,
    isProductsEqual,
}) => {
    const actions = useActions();
    const posActions = usePosActions();

    const handlePrintKitchenOrder = () =>
        PosOrderService.handleKitchenPrinter(
            order,
            orderDiff,
            actions,
            posActions,
            isProductsEqual
        );

    const handleDiscountClick = () => {
        actions.openModal({
            title: TranslateConstants.DISCOUNTS,
            component: <PosOrderDiscountModal />,
            showButtons: false,
        });
    };

    const handlePrintCheque = () => {
        PosOrderService.handleChequePrinter(order, actions);
    };

    const disableButtons = useMemo(() => {
        return (
            order.type !== OrderTypeEnum.DINE_IN ||
            (order.status === OrderStatusEnum.IN_PROGRESS && isProductsEqual) ||
            order.status === OrderStatusEnum.COMPLETED ||
            order.status === OrderStatusEnum.CANCELLED ||
            !order?.products?.length ||
            !order.table
        );
    }, [order]);

    const discountDisabled = useMemo(() => {
        return (
            order.status === OrderStatusEnum.COMPLETED ||
            order.status === OrderStatusEnum.CANCELLED ||
            !order?.products?.length
        );
    }, [order]);

    return (
        <div className="flex">
            <IconButtonComponent
                icon={<PiChefHat />}
                padding="p-2"
                rounded={false}
                iconSize="text-3xl"
                onClick={handlePrintKitchenOrder}
                isDisabled={disableButtons}
            />
            <IconButtonComponent
                icon={<RiDiscountPercentLine />}
                padding="p-2"
                rounded={false}
                iconSize="text-3xl"
                onClick={handleDiscountClick}
                isDisabled={discountDisabled}
                className="border-x"
            />
            <IconButtonComponent
                icon={<TiPrinter />}
                padding="p-2"
                rounded={false}
                iconSize="text-3xl"
                isDisabled={order.status !== OrderStatusEnum.IN_PROGRESS}
                onClick={handlePrintCheque}
            />
        </div>
    );
};

export default PosOrderControlButtonsComponent;
