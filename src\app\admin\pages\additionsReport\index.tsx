import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminAdditionsReportTableHeaders } from "./AdminMostSellingProductsReportsConstants";
import { IMostSellingProductsModel } from "../../../../common/models/MostSellingProductsModel";
import useFetch from "../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { AdditionsApiRepo } from "../../../../common/repos/api/AdditionsApiRepo";
import { ReturnedAdditionsApiRepo } from "../../../../common/repos/api/ReturnedAdditionsRepo";
import { AdminAdditionsReportService } from "./AdminMostSellingProductsReportsService";
import { useMemo } from "react";

const AdminAdditionReportPage = () => {
    const { isXs, isSm } = useScreenSize();

    const {
        data: additionsData,
        isLoading: additionsLoading,
        isError: additionsError,
        refetch: additionsRefetch,
    } = useFetch(
        EndPointsEnums.ORDER_PRODUCTS_ADDITIONS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => AdditionsApiRepo.getSum(startTime, endTime)
    );

    const {
        data: returnedData,
        isLoading: returnedLoading,
        isError: returnedError,
        refetch: returnedRefetch,
    } = useFetch(
        EndPointsEnums.RETURNED_ORDER_PRODUCTS_ADDITIONS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => ReturnedAdditionsApiRepo.getSum(startTime, endTime)
    );

    const onDate = (startDate: Date, endDate: Date) => {
        const start = DateUtils.getStartOfDayNumber(startDate);
        const end = DateUtils.getEndOfDayNumber(endDate);
        additionsRefetch(start, end);
        returnedRefetch(start, end);
    }

    const data = useMemo(() => {
        return AdminAdditionsReportService.handleData(
            additionsData,
            returnedData
        );
    }, [additionsData, returnedData]);

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent onDate={onDate} />
            <StatusComponent
                isEmpty={!data?.length}
                isLoading={additionsLoading || returnedLoading}
                isError={additionsError || returnedError}
                height={isXs ? 7.6 : isSm ? 8 : 8.1}
            >
                <TableComponent
                    headers={AdminAdditionsReportTableHeaders}
                    items={data || []}
                    selectors={(item: IMostSellingProductsModel) => [
                        item.name,
                        item.quantity,
                        item.discount.toFixed(2),
                        item.subTotal.toFixed(2),
                        item.vat.toFixed(2),
                        item.total.toFixed(2),
                    ]}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminAdditionReportPage;
