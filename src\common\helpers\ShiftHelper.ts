import { AppLocalDB } from "../config/localDB";
import { posShiftKeyConstant } from "../constants/ConfigConstants";
import { IPosShiftModel } from "../models/PosShiftModel";
import { LocalStorageHelper } from "./LocalStorageHelper";

export class ShiftHelper {
    static start(startAmount: number): IPosShiftModel {
        if (startAmount < 0) throw new Error("Shift must be greater than or equal to 0");
        const generateId = AppLocalDB.generateId();
        const shift: IPosShiftModel = {
            id: generateId,
            deleted: false,
            createdAt: new Date(),
            updatedAt: new Date(),
            shiftId: generateId,
            startAmount,
            endAmount: 0,
            startTime: new Date(),
            endTime: new Date(),
            ordersCount: 0,
            returnedOrdersCount: 0,
            totalAmount: 0,
            totalNetAmount: 0,
            totalReturnedAmount: 0,
            discountAmount: 0,
            tobaccoTaxAmount: 0,
            vatAmount: 0,
            additionAmount: 0,
            shortageAmount: 0,
            cashAmount: 0,
            networkAmount: 0,
            deliveryAppsAmount: 0,
        };
        LocalStorageHelper.set(posShiftKeyConstant, shift);
        return shift;
    }

    static get(): IPosShiftModel | undefined {
        const shift: IPosShiftModel | undefined = LocalStorageHelper.get(posShiftKeyConstant);
        return shift;
    }

    static isOpen(): boolean {
        const shift: IPosShiftModel | undefined = LocalStorageHelper.get(posShiftKeyConstant);
        return !!shift;
    }

    static end(endAmount: number): void {
        if (endAmount < 0) throw new Error("Shift must be greater than or equal to 0");
        const shift: IPosShiftModel | undefined = LocalStorageHelper.get(posShiftKeyConstant);
        if (!shift) throw new Error("Shift is not started");
        shift.endAmount = endAmount;
        shift.endTime = new Date();
        LocalStorageHelper.set(posShiftKeyConstant, shift);
    }

    static close(): void {
        LocalStorageHelper.remove(posShiftKeyConstant);
    }
}