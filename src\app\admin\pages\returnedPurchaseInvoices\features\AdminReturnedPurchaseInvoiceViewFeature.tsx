import { <PERSON><PERSON><PERSON>, FC, SetStateAction } from "react";
import AddAndFilterComponent from "../../../../../common/components/AddAndFilterComponent";
import StatusComponent from "../../../../../common/components/StatusComponent";
import TableComponent from "../../../../../common/components/TableComponent";
import useFetch from "../../../../../common/asyncController/useFetch";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import useScreenSize from "../../../../../common/hooks/useScreenSize";
import { AdminReturnedPurchaseInvoiceDataTableHeaders } from "../AdminReturnedPurchaseInvoiceConstants";
import { DateUtils } from "../../../../../common/utils/DateUtils";
import { AdminReturnedPurchaseInvoiceService } from "../AdminReturnedPurchaseInvoiceService";
import { IPurchaseInvoiceModel } from "../../../../../common/models/PurchaseInvoiceModel";
import { PurchaseInvoiceApiRepo } from "../../../../../common/repos/api/PurchaseInvoiceApiRepo";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import useActions from "../../../../../common/redux/data/useActions";
import SplitPaymentModal from "../../../../../common/modals/SplitPaymentModal";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";

interface IProps {
    setIsView: Dispatch<SetStateAction<boolean>>;
}

const AdminReturnedPurchaseInvoiceViewFeature: FC<IProps> = ({ setIsView }) => {
    const { isSm } = useScreenSize();
    const actions = useActions();

    const { data, isLoading, isError, refetch } = useFetch(
        EndPointsEnums.PURCHASE_INVOICES,
        PurchaseInvoiceApiRepo.getPurchaseInvoice
    );

    const payPurchaseInvoice = useFlatMutate(
        (body, payment) => PurchaseInvoiceApiRepo.payPurchaseInvoice(body, payment), {
        showDefaultSuccessToast: true,
        closeModalOnSuccess: true,
        updateCached: {
            key: EndPointsEnums.PURCHASE_INVOICES,
            operation: "update",
            selector: (data: IPurchaseInvoiceModel) => data.id
        },
    });

    const handleOnPrint = (item: IPurchaseInvoiceModel) =>
        AdminReturnedPurchaseInvoiceService.handlePreview(item);

    const handleOnPay = (item: IPurchaseInvoiceModel) => {
        actions.openModal({
            component: (
                <SplitPaymentModal
                    total={item.deferred}
                    onClick={(val) => payPurchaseInvoice(item, val)}
                />
            ),
            title: TranslateHelper.t(TranslateConstants.PAY) + " #" + item.number,
            size: "md",
            showButtons: false,
        });
    }

    return (
        <>
            <AddAndFilterComponent
                onAdd={() => setIsView(false)}
                onReload={refetch}
            />
            <StatusComponent
                isLoading={isLoading}
                isError={isError}
                isEmpty={!data || data.length === 0}
                height={isSm ? 7.3 : 8}
            >
                <TableComponent
                    headers={AdminReturnedPurchaseInvoiceDataTableHeaders}
                    items={data || []}
                    selectors={(item: IPurchaseInvoiceModel) => [
                        item.number,
                        item.supplier?.name,
                        item.total,
                        item.deferred,
                        DateUtils.format(item.date),
                    ]}
                    showEditButton={false}
                    showPrintButton={true}
                    onPrint={handleOnPrint}
                    customButtons={[
                        {
                            text: TranslateConstants.PAY,
                            onClick: handleOnPay,
                            showButton: (item: IPurchaseInvoiceModel) => !!item.deferred,
                        },
                    ]}
                />
            </StatusComponent>
        </>
    );
}

export default AdminReturnedPurchaseInvoiceViewFeature;
