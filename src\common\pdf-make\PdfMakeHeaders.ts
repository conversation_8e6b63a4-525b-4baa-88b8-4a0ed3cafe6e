import { Content } from "pdfmake/interfaces"
import { OrganizationHelper } from "../helpers/OrganizationHelper"
import { imageUrlToBase64 } from "../utils/CommonUtils"
import { PdfMakeHelper } from "./PdfMakeHelper"
import { IsArabic } from "../hooks/useTranslate"

export class PdfMakeHeaders {
    static normal = async (
        isLandScape?: boolean
    ): Promise<Content> => {
        return {
            columns: [
                {
                    stack: [
                        PdfMakeHelper.optionalDocItem(!!OrganizationHelper.getOrganization()?.name, [
                            { text: OrganizationHelper.getOrganization()?.name || "", style: { fontSize: 14, bold: true } },
                        ]),
                        PdfMakeHelper.optionalDocItem(!!OrganizationHelper.getOrganization()?.taxNumber, [
                            { text: `الرقم الضريبي: ${OrganizationHelper.getOrganization()?.taxNumber || ""}`, style: { fontSize: 12 } },
                        ]),
                        PdfMakeHelper.optionalDocItem(!!OrganizationHelper.getOrganization()?.registrationNumber, [
                            { text: `السجل التجاري: ${OrganizationHelper.getOrganization()?.registrationNumber || ""}`, style: { fontSize: 12 } },
                        ]),
                    ],
                    alignment: 'right',
                    style: { lineHeight: 1.5 },
                    width: "85%",
                    margin: [0, 10, 0, 0],
                },
                PdfMakeHelper.optionalDocItem(!!OrganizationHelper.getOrganization()?.logo, [
                    {
                        image: await imageUrlToBase64(OrganizationHelper.getOrganization()?.logo || ""),
                        width: 60,
                        height: 60,
                        margin: [0, 5, isLandScape ? 15 : 0, 0],
                        alignment: !isLandScape ? "right" : undefined,
                    },
                ]),
            ],
            margin: [10, 10, 20, 0],
        }
    }

    static invoice = async (
        data: {
            titleAr: string;
            titleEn: string;
            supplierName: string;
            supplierMobile: string;
            supplierTaxNumber: string;
            supplierAddress: string;
            InvoiceNumber: string;
            startDate: string;
            endDate: string;
            qrCode: string;
        },
    ): Promise<Content> => {
        const isArabic = IsArabic();

        return {
            stack: [
                {
                    columns: [
                        {
                            stack: [
                                PdfMakeHelper.optionalDocItem(!!OrganizationHelper.getOrganization()?.nameEn, [
                                    { text: OrganizationHelper.getOrganization()?.nameEn || "", style: { fontSize: 14, bold: true } },
                                ]),
                                PdfMakeHelper.optionalDocItem(!!OrganizationHelper.getOrganization()?.taxNumber, [
                                    { text: `VAT No: ${OrganizationHelper.getOrganization()?.taxNumber || ""}`, style: { fontSize: 12 } },
                                ]),
                                PdfMakeHelper.optionalDocItem(!!OrganizationHelper.getOrganization()?.registrationNumber, [
                                    { text: `C.R No: ${OrganizationHelper.getOrganization()?.registrationNumber || ""}`, style: { fontSize: 12 } },
                                ]),
                            ],
                            alignment: 'left',
                            style: { lineHeight: 1.5 },
                            margin: [0, 10, 0, 0],
                        },
                        PdfMakeHelper.optionalDocItem(!!OrganizationHelper.getOrganization()?.logo, [
                            {
                                image: await imageUrlToBase64(OrganizationHelper.getOrganization()?.logo || ""),
                                width: 60,
                                height: 60,
                                margin: [0, 5, 0, 0],
                                alignment: "center",
                            },
                        ]),
                        {
                            stack: [
                                PdfMakeHelper.optionalDocItem(!!OrganizationHelper.getOrganization()?.name, [
                                    { text: OrganizationHelper.getOrganization()?.name || "", style: { fontSize: 14, bold: true } },
                                ]),
                                PdfMakeHelper.optionalDocItem(!!OrganizationHelper.getOrganization()?.taxNumber, [
                                    { text: `الرقم الضريبي: ${OrganizationHelper.getOrganization()?.taxNumber || ""}`, style: { fontSize: 12 } },
                                ]),
                                PdfMakeHelper.optionalDocItem(!!OrganizationHelper.getOrganization()?.registrationNumber, [
                                    { text: `السجل التجاري: ${OrganizationHelper.getOrganization()?.registrationNumber || ""}`, style: { fontSize: 12 } },
                                ]),
                            ],
                            alignment: 'right',
                            style: { lineHeight: 1.5 },
                            margin: [0, 10, 0, 0],
                        },
                    ],
                },
                { text: isArabic ? data.titleAr : data.titleEn, alignment: "center", bold: true, color: "#226bb2", marginTop: 20, marginBottom: 20 },
                {
                    fontSize: 10,
                    bold: true,
                    alignment: "right",
                    margin: 2,
                    lineHeight: 1.5,
                    layout: "noBorders",
                    table: {
                        widths: ["*", "*"],
                        body: [
                            [
                                {
                                    columns: [
                                        { text: data.supplierMobile, width: "*", marginRight: 10 },
                                        { text: PdfMakeHelper.reverseTextIfArabic("جوال  المــــــورد:"), 
                                            width: "auto" },
                                    ],
                                },
                                {
                                    columns: [
                                        { text: PdfMakeHelper.reverseTextIfArabic(data.supplierName), width: "*", marginRight: 10 },
                                        { text: PdfMakeHelper.reverseTextIfArabic("اســــم  المــــــورد:"), width: "auto" },
                                    ],
                                },
                            ],
                            [
                                {
                                    columns: [
                                        { text: data.InvoiceNumber.trim(), width: "*", marginRight: 10 },
                                        { text: PdfMakeHelper.reverseTextIfArabic("رقـــــم  الفاتورة: "), width: "auto" },
                                    ],
                                },
                                {
                                    columns: [
                                        { text: PdfMakeHelper.reverseTextIfArabic(data.supplierAddress), width: "*", marginRight: 10 },
                                        { text: PdfMakeHelper.reverseTextIfArabic("عنوان  المــــــورد:"), width: "auto" },
                                    ],
                                },
                            ],
                            [
                                {
                                    columns: [
                                        { text: data.endDate + " - " + data.startDate, width: "*", marginRight: 10 },
                                        { text: PdfMakeHelper.reverseTextIfArabic("تاريخ  الفاتورة:"), width: "auto" },
                                    ],
                                },
                                {
                                    columns: [
                                        { text: data.supplierTaxNumber, width: "*", marginRight: 10 },
                                        { text: PdfMakeHelper.reverseTextIfArabic("الرقم  الضريبي:"), width: "auto" },
                                    ],
                                },
                            ],
                        ],
                    },
                }
            ],
            margin: [10, 10, 20, 0],
        }
    }
}