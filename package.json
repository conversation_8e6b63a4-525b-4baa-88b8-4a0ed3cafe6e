{"name": "restaurant-app-client-site", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "tsc": "tsc -b", "fire-cors": "node scripts/fireCors.cjs"}, "dependencies": {"@reduxjs/toolkit": "^2.2.7", "@types/pdfmake": "^0.2.11", "axios": "^1.7.7", "comlink": "^4.4.2", "firebase": "^10.6.0", "i": "^0.3.7", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.2", "i18next-http-backend": "^3.0.2", "js-cookie": "^3.0.5", "npm": "^11.1.0", "pdfmake": "^0.2.20", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-i18next": "^15.4.0", "react-icons": "^5.3.0", "react-qr-code": "^2.0.15", "react-redux": "^9.1.2", "react-tailwindcss-datepicker": "^1.7.2", "react-to-print": "^2.15.1", "react-toastify": "^10.0.5", "theme-change": "^2.5.0", "uuid": "^11.0.0", "written-number": "^0.11.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/js-cookie": "^3.0.6", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "daisyui": "^2.41.0", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "react-router-dom": "^6.26.2", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5", "vite-plugin-comlink": "^5.1.0", "vite-plugin-pwa": "^1.0.0"}}