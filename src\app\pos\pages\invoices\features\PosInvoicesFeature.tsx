import { useNavigate } from "react-router-dom";
import useFetch from "../../../../../common/asyncController/useFetch";
import StatusComponent from "../../../../../common/components/StatusComponent";
import TableComponent from "../../../../../common/components/TableComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { CachedKeysEnums } from "../../../../../common/enums/CachedKeysEnums";
import { OrderStatusEnum } from "../../../../../common/enums/DataEnums";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { useTranslate } from "../../../../../common/hooks/useTranslate";
import { IPosOrderModel } from "../../../../../common/models/PosOrderModel";
import useActions from "../../../../../common/redux/data/useActions";
import { getOrderType } from "../../../../../common/utils/CommonUtils";
import { DateUtils } from "../../../../../common/utils/DateUtils";
import usePosActions from "../../../redux/usePosActions";
import { PosOrderRepo } from "../../../repo/PosOrderRepo";
import { PosTablesService } from "../../tables/PosTablesService";
import PosInvoicesReturnOrderModal from "../modals/PosInvoicesReturnOrderModal";
import { PosInvoicesTableHeaderConstants } from "../PosInvoicesConstants";
import { PosInvoicesService } from "../PosInvoicesService";

const PosInvoicesFeature = () => {
    const actions = useActions();
    const posActions = usePosActions();
    const navigate = useNavigate();
    const { translate } = useTranslate();
    const { data, isLoading, isError } = useFetch(
        CachedKeysEnums.POS_INVOICES,
        PosOrderRepo.getShiftOrders,
        { autoFetchOnMount: true }
    );

    const handleOnPrint = async (order: IPosOrderModel) =>
        PosInvoicesService.handleOnPrint(actions, order);

    const handleOnReturn = async (order: IPosOrderModel) => {
        actions.openModal({
            size: "2xl",
            title: TranslateHelper.t(TranslateConstants.RETURN_ORDER) + " #" + order.invoiceNumber,
            showButtons: false,
            component: <PosInvoicesReturnOrderModal order={order} />,
        });
    };

    const handleOnPrintReturned = async (order: IPosOrderModel) => {
        const returnedOrder = await PosOrderRepo.getRelatedReturnedOrder(order);
        if (!returnedOrder) return;
        PosInvoicesService.handleOnPrintReturned(actions, returnedOrder);
    };

    const handleOnView = async (order: IPosOrderModel) =>
        PosTablesService.handleViewTable(undefined, order, posActions, navigate);

    return (
        <StatusComponent
            isLoading={isLoading}
            isError={isError}
            isEmpty={!data || data.length === 0}
            height={13}
        >
            <TableComponent
                headers={PosInvoicesTableHeaderConstants}
                items={data || []}
                selectors={(item: IPosOrderModel) => {
                    const statusColor = () => {
                        if (
                            item.status === OrderStatusEnum.PENDING ||
                            item.status === OrderStatusEnum.RETURNED
                        ) return "text-yellow-600";
                        else if (item.status === OrderStatusEnum.COMPLETED) return "text-green-600";
                        else if (item.status === OrderStatusEnum.CANCELLED) return "text-red-600";
                        return ""
                    }

                    return [
                        item.invoiceNumber,
                        item.orderNumber,
                        getOrderType(item),
                        item.table?.name,
                        item.customer?.name,
                        item.customer?.mobile,
                        item.cash.toFixed(2),
                        item.network.toFixed(2),
                        item.total.toFixed(2),
                        DateUtils.format(item.createdAt),
                        <div className={statusColor()}>{translate(item.status)}</div>,
                    ]
                }}
                showPrintButton={(item: IPosOrderModel) => {
                    return (
                        item.status === OrderStatusEnum.COMPLETED ||
                        item.status === OrderStatusEnum.RETURNED
                    );
                }}
                onPrint={handleOnPrint}
                customButtons={[
                    {
                        text: translate(TranslateConstants.RETURNED),
                        onClick: handleOnReturn,
                        showButton: (item: IPosOrderModel) => {
                            return item.status === OrderStatusEnum.COMPLETED && !!item.orderId
                        },
                        bgColor: "slate",
                    },
                    {
                        text: translate(TranslateConstants.PRINT_RETURNED),
                        onClick: handleOnPrintReturned,
                        showButton: (item: IPosOrderModel) => {
                            return item.status === OrderStatusEnum.RETURNED
                        },
                        bgColor: "slate",
                    },
                    {
                        text: translate(TranslateConstants.VIEW),
                        onClick: handleOnView,
                        showButton: (item: IPosOrderModel) => {
                            return item.status === OrderStatusEnum.IN_PROGRESS
                        },
                        bgColor: "transparent",
                        borderColor: "primary",
                        textColor: "primary",
                    },
                ]}
            />
        </StatusComponent>
    );
};

export default PosInvoicesFeature;
