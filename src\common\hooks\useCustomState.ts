import { Dispatch, SetStateAction, useEffect, useState } from "react";

type State = string | number | boolean | object | any[];

const handleInitialState = (
    initialState: any,
    updateState?: any,
    explodeFromUpdate: any[] = [],
    updateSelector?: (state: any) => any
) => {
    if (updateState) {
        if (updateSelector) return updateSelector(updateState);
        if (typeof initialState === "object" && typeof updateState === "object") {
            const initKeys = Object.keys(initialState);
            const updateKeys = Object.keys(updateState);
            const res = { ...initialState };

            for (let key of initKeys) {
                if (updateKeys.includes(key) && !explodeFromUpdate.includes(key)) {
                    res[key] = updateState[key];
                }
            }

            return res;
        }

        return updateState;
    }
    return initialState;
};

const useCustomState = <T, U = any>(
    initialState: State,
    options?: {
        updateState?: State,
        explodeFromReset?: (keyof T)[],
        explodeFromUpdate?: (keyof T)[],
        updateSelector?: (state: U) => T,
        resetStateData?: State
    }
): [T, Dispatch<SetStateAction<T>>, () => void] => {
    const resetStateData = options?.resetStateData ?? initialState;
    const [state, setState] = useState<T>(
        handleInitialState(
            initialState,
            options?.updateState,
            options?.explodeFromUpdate,
            options?.updateSelector
        ) as T
    );

    useEffect(() => {
        setState(
            handleInitialState(
                initialState,
                options?.updateState,
                options?.explodeFromUpdate,
                options?.updateSelector
            ) as T
        );
    }, [options?.updateState]);

    const resetState = () => {
        if (options?.explodeFromReset?.length && typeof resetStateData === "object") {
            setState((prev: T) => {
                const resetState = { ...resetStateData } as T;
                for (let key of options.explodeFromReset!) {
                    (resetState as any)[key] = (prev as any)[key];
                }
                return resetState;
            });
            return;
        }
        setState(handleInitialState(resetStateData) as T);
    };

    return [state, setState, resetState];
};

export default useCustomState;
