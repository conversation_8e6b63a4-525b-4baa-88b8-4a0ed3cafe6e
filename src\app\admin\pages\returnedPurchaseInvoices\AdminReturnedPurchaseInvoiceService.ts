import { UpdateFetch } from "../../../../common/asyncController/updateFetch";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { <PERSON>r<PERSON>elper } from "../../../../common/helpers/QrHelper";
import { IPurchaseInvoiceModel } from "../../../../common/models/PurchaseInvoiceModel";
import { IPurchaseOrderModel } from "../../../../common/models/PurchaseOrderModel";
import { PdfMakeHeaders } from "../../../../common/pdf-make/PdfMakeHeaders";
import { PdfMakeUtils } from "../../../../common/pdf-make/PdfMakeUtils";
import { PurchaseInvoicePdfPrinterContent } from "../../../../common/pdf-make/slices/purchaseInvoice/PurchaseInvoicePdfPrinterContent";
import { IPurchaseInvoicePdfPrinterModel } from "../../../../common/pdf-make/slices/purchaseInvoice/PurchaseInvoiceReportPdfPrinterModel";
import { DateUtils } from "../../../../common/utils/DateUtils";
import { fixedNumber } from "../../../../common/utils/numberUtils";

export class AdminReturnedPurchaseInvoiceService {
    static async handlePreview(purchaseInvoice: IPurchaseInvoiceModel) {
        const model: IPurchaseInvoicePdfPrinterModel = {
            items: purchaseInvoice.purchaseInvoiceProducts?.map((el) => ({
                name: el.name,
                quantity: fixedNumber(el.quantity),
                price: fixedNumber(el.price),
                discount: fixedNumber(el.discount),
                subTotal: el.subTotal,
                vat: el.vat,
                total: fixedNumber(el.total),
            })),
            note: purchaseInvoice.note,
            totals: {
                nativeTotal: purchaseInvoice.nativeTotal,
                productsDiscount: purchaseInvoice.productsDiscount,
                subTotal: purchaseInvoice.subTotal,
                vat: purchaseInvoice.vat,
                total: purchaseInvoice.total,
            },
        };

        PdfMakeUtils.preview(PurchaseInvoicePdfPrinterContent(model), {
            headerType: "invoice",
            header: await PdfMakeHeaders.invoice({
                InvoiceNumber: purchaseInvoice.number.toString(),
                titleAr: "فاتورة مشتريات",
                titleEn: "Purchase Invoice",
                startDate: DateUtils.format(purchaseInvoice.date, "dd/MM/yyyy"),
                endDate: DateUtils.format(purchaseInvoice.dueDate, "dd/MM/yyyy"),
                supplierName: purchaseInvoice.supplier?.name || "",
                supplierMobile: purchaseInvoice.supplier?.mobile || "",
                supplierTaxNumber: purchaseInvoice.supplier?.taxNumber || "",
                supplierAddress: purchaseInvoice.supplier?.address || "",
                qrCode: QrHelper.vatQrData(purchaseInvoice.total, purchaseInvoice.vat),
            }),
        });
    }

    static async updatePurchaseOrderConversion(purchaseOrderId: number) {
        UpdateFetch.dynamicUpdate(EndPointsEnums.PURCHASE_ORDERS, async (data: IPurchaseOrderModel[]) => {
            return data?.map((item: IPurchaseOrderModel) => {
                if (item.id === purchaseOrderId) return { ...item, isConverted: true };
                return item;
            });
        });
    }
}
