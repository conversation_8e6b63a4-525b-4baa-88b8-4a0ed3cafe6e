import { <PERSON><PERSON><PERSON>, FC, SetStateAction, useEffect, useMemo } from "react";
import useFetch from "../../../../../common/asyncController/useFetch";
import AddAndFilterComponent from "../../../../../common/components/AddAndFilterComponent";
import DropDownSearchComponent from "../../../../../common/components/DropDownSearchComponent";
import InputComponent from "../../../../../common/components/InputComponent";
import ListComponent from "../../../../../common/components/ListComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import useScreenSize from "../../../../../common/hooks/useScreenSize";
import {
    AdminReturnedPurchaseInvoiceHeaders,
    AdminReturnedPurchaseInvoiceInitialState,
    AdminReturnedPurchaseInvoiceInputs,
} from "../AdminReturnedPurchaseInvoiceConstants";
import {
    IAdminReturnedPurchaseInvoiceInputs,
    IAdminReturnedPurchaseInvoiceState,
} from "../AdminReturnedPurchaseInvoiceInterfaces";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { AdminReturnedPurchaseInvoiceValidation } from "../AdminReturnedPurchaseInvoiceValidation";
import useCustomState from "../../../../../common/hooks/useCustomState";
import StatusComponent from "../../../../../common/components/StatusComponent";
import { PurchaseUtils } from "../../../../../common/utils/PurchaseUtils";
import useDerivedState from "../../../../../common/hooks/useDerivedState";
import AdminMultiplePurchasesProductInputsComponent from "../../../components/AdminMultiplePurchasesProductInputsComponent";
import { AdminReturnedPurchaseInvoiceService } from "../AdminReturnedPurchaseInvoiceService";
import { IPurchaseInvoiceModel } from "../../../../../common/models/PurchaseInvoiceModel";
import { AdminReturnedPurchaseInvoiceUtil } from "../AdminReturnedPurchaseInvoiceUtil";
import { PurchaseInvoiceApiRepo } from "../../../../../common/repos/api/PurchaseInvoiceApiRepo";
import useActions from "../../../../../common/redux/data/useActions";
import SplitPaymentModal, {
    ISplitPaymentState,
    SplitPaymentInitialState,
} from "../../../../../common/modals/SplitPaymentModal";
import { PaymentsEnum } from "../../../../../common/enums/DataEnums";
import { maxStringLength } from "../../../../../common/utils/CommonUtils";

interface IProps {
    setIsView: Dispatch<SetStateAction<boolean>>;
    convertedItem: IAdminReturnedPurchaseInvoiceState | undefined;
    purchaseOrderId: number | undefined;
}

const AdminReturnedPurchaseInvoiceInputsFeature: FC<IProps> = ({
    setIsView,
    convertedItem,
    purchaseOrderId,
}) => {
    const { isSm } = useScreenSize();
    const actions = useActions();
    const [state, setState, resetState] = useCustomState<
        IAdminReturnedPurchaseInvoiceState,
        IPurchaseInvoiceModel
    >(convertedItem ?? AdminReturnedPurchaseInvoiceInitialState, {
        explodeFromReset: ["supplier"],
        resetStateData: AdminReturnedPurchaseInvoiceInitialState,
    });
    const oldState = useDerivedState(state);

    const {
        data: purchaseInvoices,
        isLoading: purchaseInvoicesIsLoading,
        isError: purchaseInvoicesIsError,
    } = useFetch(EndPointsEnums.PURCHASE_INVOICES, (search, limit = 100) =>
        PurchaseInvoiceApiRepo.getPurchaseInvoice({ search, limit })
    );

    const addPurchaseInvoice = useFlatMutate(
        (body, payment, purchaseOrderId) =>
            PurchaseInvoiceApiRepo.addPurchaseInvoice(body, payment, purchaseOrderId),
        {
            showDefaultSuccessToast: true,
            updateCached: { key: EndPointsEnums.PURCHASE_INVOICES, operation: "add" },
            closeModalOnSuccess: true,
            onSuccess: async ({ args, data }) => {
                if (args[2])
                    AdminReturnedPurchaseInvoiceService.updatePurchaseOrderConversion(
                        args[2]
                    );
                if (args[3])
                    await AdminReturnedPurchaseInvoiceService.handlePreview(data);
            },
            afterEnd: () => resetState(),
        }
    );

    useEffect(() => {
        setState((prev) => ({
            ...prev,
            ...PurchaseUtils.getPurchaseData(
                prev.purchaseInvoiceProducts,
                prev.isPriceIncludingTax
            ),
        }));
    }, [state.purchaseInvoiceProducts, state.isPriceIncludingTax]);

    const multiInputs = useMemo(() => {
        return AdminReturnedPurchaseInvoiceInputs(state.isPriceIncludingTax);
    }, [state.isPriceIncludingTax]);

    const handleOnPurchaseInvoiceProductsResult = (
        result: IAdminReturnedPurchaseInvoiceInputs[]
    ) => {
        setState((prev) => ({
            ...prev,
            purchaseInvoiceProducts:
                AdminReturnedPurchaseInvoiceUtil.handlePurchaseInvoiceProductsData(
                    result,
                    prev.isPriceIncludingTax
                ),
        }));
    };

    const handlePayment = (payment: ISplitPaymentState, isPrint: boolean) =>
        addPurchaseInvoice(state, payment, purchaseOrderId, isPrint);

    const handleOnClick = (isPrint: boolean) => {
        if (!AdminReturnedPurchaseInvoiceValidation.inputsValidation(state)) return;
        if (state.paymentMethod !== PaymentsEnum.CREDIT) {
            actions.openModal({
                component: (
                    <SplitPaymentModal
                        total={state.total}
                        onClick={(val) => handlePayment(val, isPrint)}
                        paymentType={state.paymentMethod}
                        date={state.dueDate}
                    />
                ),
                title: TranslateConstants.PAY,
                size: "md",
                showButtons: false,
            });
            return;
        }

        handlePayment(
            SplitPaymentInitialState(state.total, undefined, state.paymentMethod),
            isPrint
        );
    };

    return (
        <>
            <AddAndFilterComponent
                onSave={() => handleOnClick(false)}
                onSaveAndPrint={() => handleOnClick(true)}
                onBack={() => setIsView(true)}
            />
            <StatusComponent
                isLoading={purchaseInvoicesIsLoading}
                isError={purchaseInvoicesIsError}
                height={isSm ? 7.5 : 8.1}
                className="!p-0"
            >
                <ListComponent
                    padding="0 px-2"
                    isBorder={true}
                    className="bg-base-100 !border-none"
                >
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                        <DropDownSearchComponent
                            label={TranslateConstants.INVOICE_NUMBER}
                            items={purchaseInvoices || []}
                            titleSelector={(item: IPurchaseInvoiceModel) => item.number + " - " + item.supplier?.name}
                            subtitleSelector={(item: IPurchaseInvoiceModel) => maxStringLength(item.note)}
                            isOutFilter={true}
                        />
                        <InputComponent
                            label={TranslateConstants.DATE}
                            type="fixed"
                            value="de"
                            className="text-gray-400"
                            />
                        <InputComponent
                            label={TranslateConstants.DUE_DATE}
                            type="fixed"
                            className="text-gray-400"
                            />
                        <InputComponent
                            label={TranslateConstants.PAYMENT_METHOD}
                            type="fixed"
                            className="text-gray-400"
                            />
                        <InputComponent
                            label={TranslateConstants.SUPPLIER}
                            type="fixed"
                            className="text-gray-400"
                            />
                        <InputComponent
                            label={TranslateConstants.VAT_TYPE}
                            type="fixed"
                            className="text-gray-400"
                        />
                    </div>
                    <AdminMultiplePurchasesProductInputsComponent<IAdminReturnedPurchaseInvoiceInputs>
                        headers={AdminReturnedPurchaseInvoiceHeaders}
                        inputs={multiInputs}
                        onResult={handleOnPurchaseInvoiceProductsResult}
                        defaultValues={state.purchaseInvoiceProducts}
                        state={state}
                        setState={setState}
                        resetInputs={
                            state.purchaseInvoiceProducts.length === 0 ||
                            state.isPriceIncludingTax !== oldState.isPriceIncludingTax
                        }
                    />
                </ListComponent>
            </StatusComponent>
        </>
    );
};

export default AdminReturnedPurchaseInvoiceInputsFeature;
