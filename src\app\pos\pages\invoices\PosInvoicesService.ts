import { UpdateFetch } from "../../../../common/asyncController/updateFetch";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { CachedKeysEnums } from "../../../../common/enums/CachedKeysEnums";
import { OrderStatusEnum } from "../../../../common/enums/DataEnums";
import { ReturnedOrderHelper } from "../../../../common/helpers/ReturnedOrderHelper";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IPosOrderModel } from "../../../../common/models/PosOrderModel";
import { IPosReturnedOrderModel } from "../../../../common/models/PosReturnedOrderModel";
import { IActions } from "../../../../common/redux/data/useActions";
import { debug } from "../../../../common/utils/CommonUtils";
import { PosOrderService } from "../../containers/order/PosOrderService";
import { PosOrderUtils } from "../../containers/order/PosOrderUtils";
import { IPosActions } from "../../redux/usePosActions";
import { PosOrderRepo } from "../../repo/PosOrderRepo";
import { PosPrintersRepo } from "../../repo/PosPrintersRepo";
import { PosReturnedOrdersRepo } from "../../repo/PosReturnedOrdersRepo";

export class PosInvoicesService {
    static async handleOnPrint(actions: IActions, order: IPosOrderModel) {
        try {
            actions.setLoading();
            if (
                order.status !== OrderStatusEnum.COMPLETED &&
                order.status !== OrderStatusEnum.RETURNED
            ) {
                ToastHelper.error(TranslateConstants.ERROR_PRINT_INVOICE_ORDER_STATUS);
                return;
            }
            await PosPrintersRepo.printInvoiceOrder(order);
        } catch (error) {
            debug(`PosInvoicesService [handleOnPrint] Error: ${error}`);
            ToastHelper.error(TranslateConstants.ERROR_PRINT_INVOICE_ORDER);
        } finally {
            actions.setLoading(false);
        }
    }

    static async handleOnPrintReturned(actions: IActions, returnedOrder: IPosReturnedOrderModel) {
        try {
            actions.setLoading();
            await PosPrintersRepo.printReturnedInvoiceOrder(returnedOrder);
        } catch (error) {
            debug(`PosInvoicesService [handleOnPrintReturned] Error: ${error}`);
            ToastHelper.error(TranslateConstants.ERROR_PRINT_RETURN_INVOICE_ORDER);
        } finally {
            actions.setLoading(false);
        }
    }

    static async handleOnSubmitReturnOrder(
        oldOrder: IPosOrderModel,
        returnedOrder: IPosReturnedOrderModel,
        cash: number,
        network: number,
        actions: IActions,
        posActions: IPosActions
    ) {
        try {
            if (!PosOrderService.checkShift()) return;
            actions.setLoading();
            const formattedReturnedOrder = await PosOrderUtils.handleReturnOrderData(returnedOrder, cash, network);
            await PosReturnedOrdersRepo.addReturnedOrder(formattedReturnedOrder);
            ReturnedOrderHelper.incrementReturnedInvoiceNumber();
            await PosOrderRepo.updateOrderStatus(oldOrder);
            await UpdateFetch.update<IPosOrderModel>(
                CachedKeysEnums.POS_INVOICES,
                { ...oldOrder, status: OrderStatusEnum.RETURNED },
                (data: IPosOrderModel) => data.id === returnedOrder.id
            );
            await PosPrintersRepo.printReturnedInvoiceOrder(formattedReturnedOrder);
        } catch (error) {
            debug(`PosInvoicesService [handleOnSubmitReturnOrder] Error: ${error}`);
            ToastHelper.error(TranslateConstants.ERROR_PRINT_RETURN_INVOICE_ORDER);
        } finally {
            actions.setLoading(false);
            actions.closeModal();
            PosOrderService.resetOrder(posActions);
        }
    }
}
