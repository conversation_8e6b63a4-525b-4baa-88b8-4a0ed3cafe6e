import useFetch from "../../../../common/asyncController/useFetch";
import useFlatMutate from "../../../../common/asyncController/useFlatMutate";
import AddAndFilterComponent from "../../../../common/components/AddAndFilterComponent";
import StatusComponent from "../../../../common/components/StatusComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { ICustomerModel } from "../../../../common/models/CustomerModel";
import useActions from "../../../../common/redux/data/useActions";
import { CustomersApiRepo } from "../../../../common/repos/api/CustomerApiRepo";
import { AdminCustomerDataTableHeaders } from "./AdminCustomersConstants";
import AdminCustomersModal from "./modals/AdminCustomersModal";

const AdminCustomersPage = () => {
    const actions = useActions();
    const { isSm } = useScreenSize();

    const { data, isLoading, isError, refetch } = useFetch(
        EndPointsEnums.CUSTOMERS,
        CustomersApiRepo.getCustomers
    );

    const updateCustomer = useFlatMutate(CustomersApiRepo.updateCustomer, {
        updateCached: {
            key: EndPointsEnums.CUSTOMERS,
            operation: "update",
            selector: (data: ICustomerModel) => data.id,
        },
    });

    const handleOnActive = (active: boolean, item: ICustomerModel) =>
        updateCustomer(item.id, { active });

    const handleOnClick = (isEdit?: boolean, item?: ICustomerModel) => {
        actions.openModal({
            component: <AdminCustomersModal item={item} />,
            title: isEdit ? TranslateConstants.EDIT : TranslateConstants.ADD,
            size: "md",
            showButtons: false,
        });
    };

    return (
        <>
            <AddAndFilterComponent onAdd={handleOnClick} onReload={refetch} />
            <StatusComponent
                isLoading={isLoading}
                isError={isError}
                isEmpty={!data || data.length === 0}
                height={isSm ? 7.3 : 8}
            >
                <TableComponent
                    headers={AdminCustomerDataTableHeaders}
                    items={data || []}
                    selectors={(item: ICustomerModel) => [
                        item.number,
                        item.name,
                        item.mobile,
                        item.taxNumber,
                        item.address,
                        item.orderCount,
                        item.totalDiscount.toFixed(2),
                        item.totalAmount.toFixed(2),
                    ]}
                    showEditButton={true}
                    onEdit={(item: ICustomerModel) => handleOnClick(true, item)}
                    showActiveButton={true}
                    activeSelector={(item: ICustomerModel) => item.active}
                    onActive={handleOnActive}
                />
            </StatusComponent>
        </>
    );
};

export default AdminCustomersPage;
