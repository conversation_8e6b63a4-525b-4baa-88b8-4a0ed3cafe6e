import { useEffect, useState } from "react";
import AdminReturnedPurchaseInvoiceInputsFeature from "./features/AdminReturnedPurchaseInvoiceInputsFeature";
import AdminReturnedPurchaseInvoiceViewFeature from "./features/AdminReturnedPurchaseInvoiceViewFeature";
import { useLocation, useNavigate } from "react-router-dom";
import { IPurchaseOrderModel } from "../../../../common/models/PurchaseOrderModel";
import { AdminReturnedPurchaseInvoiceUtil } from "./AdminReturnedPurchaseInvoiceUtil";
import { IAdminReturnedPurchaseInvoiceState } from "./AdminReturnedPurchaseInvoiceInterfaces";

const AdminReturnedPurchaseInvoicePage = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [isView, setIsView] = useState(false);
    const [convertedItem, setConvertedItem] = useState<
        IAdminReturnedPurchaseInvoiceState | undefined
    >(undefined);
    const [purchaseOrderId, setPurchaseOrderId] = useState<number | undefined>(undefined);

    useEffect(() => {
        if (isView) {
            setConvertedItem(undefined);
            navigate(".", { state: undefined });
            setPurchaseOrderId(undefined);
        }
    }, [isView]);

    useEffect(() => {
        if (location.state) {
            setConvertedItem(
                AdminReturnedPurchaseInvoiceUtil.convertPurchaseOrderToPurchaseInvoice(
                    location.state as IPurchaseOrderModel
                )
            );
            setPurchaseOrderId((location.state as IPurchaseOrderModel).id);
            setIsView(false);
        }
    }, [location.state]);

    return (
        <>
            {isView && (
                <AdminReturnedPurchaseInvoiceViewFeature
                    setIsView={setIsView}
                />
            )}
            {!isView && (
                <AdminReturnedPurchaseInvoiceInputsFeature
                    setIsView={setIsView}
                    convertedItem={convertedItem}
                    purchaseOrderId={purchaseOrderId}
                />
            )}
        </>
    );
};

export default AdminReturnedPurchaseInvoicePage;
