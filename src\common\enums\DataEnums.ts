export enum OrderTypeEnum {
    DINE_IN = 'DINE_IN',
    TAKE_AWAY = 'TAKE_AWAY',
    DELIVERY = 'DELIVERY',
    DELIVERY_APP = 'DELIVERY_APP',
}

export enum OrderStatusEnum {
    PENDING = 'PENDING',
    IN_PROGRESS = 'IN_PROGRESS',
    COMPLETED = 'COMPLETED',
    CANCELLED = 'CANCELLED',
    RETURNED = 'RETURNED',
}

export enum PrinterEnum {
    RECEIPT = 'RECEIPT',
}

export enum PaymentsEnum {
    CASH = 'CASH',
    BANK = 'BANK',
    CREDIT = 'CREDIT',
}

export enum TableStatusEnum {
    OCCUPIED = 'OCCUPIED',
    AVAILABLE = 'AVAILABLE',
}

export enum KitchenOrderStatusEnum {
    NEW = 'NEW',
    EDIT = 'EDIT',
    DELETE = 'DELETE',
}

export enum ProductSizeTypeEnum {
    FIXED = 'fixed',
    MULTIPLE = 'multiple',
}

export enum OrganizationRoleEnum {
    ADMIN = 'ADMIN',
    POS = 'POS',
}

export enum DeliveryAppEnum {
    HUNGER_STATION = 'HUNGER_STATION',
    JAHEZ = 'JAHEZ',
    TO_YOU = 'TO_YOU',
    THE_CHEFZ = 'THE_CHEFZ',
    KEETA = 'KEETA',
    NINJA = 'NINJA',
    MESHWAR_FOOD = 'MESHWAR_FOOD',
    LOCATE = 'LOCATE',
    NOON_FOOD = 'NOON_FOOD',
}