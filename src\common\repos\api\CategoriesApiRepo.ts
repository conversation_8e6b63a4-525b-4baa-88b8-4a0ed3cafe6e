import { IAdminCategoryInputs } from "../../../app/admin/pages/categories/AdminCategoriesInterface";
import { uploadImage } from "../../config/firebase";
import { EndPointsEnums } from "../../enums/EndPointsEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { ICategoryBody } from "../../interfaces/body/CategoryBody";
import { ICategoryModel } from "../../models/CategoryModel";
import { debug } from "../../utils/CommonUtils";

export class CategoriesApiRepo {
    static async getCategories() {
        try {
            const res = await AxiosHelper.get<ICategoryModel[]>(EndPointsEnums.CATEGORIES);
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`CategoriesApiRepo [getCategories] Error: `, error);
            throw error;
        }
    }

    static async addCategory(inputs: IAdminCategoryInputs) {
        try {
            const body: ICategoryBody = {
                name: inputs.name || "",
                image: "",
                active: inputs.active,
            }
            if (inputs.image) body.image = await uploadImage(inputs.image);
            const res = await AxiosHelper.post<ICategoryModel>(EndPointsEnums.CATEGORIES, body);
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`CategoriesApiRepo [addCategory] Error: `, error);
            throw error;
        }
    }

    static async updateCategory(id: number, inputs: IAdminCategoryInputs) {
        try {
            const body: Partial<ICategoryBody> = {
                name: inputs.name,
                active: inputs.active,
            }
            if (inputs.image) body.image = await uploadImage(inputs.image);
            const res = await AxiosHelper.patch<ICategoryModel>(EndPointsEnums.CATEGORIES, id, body);
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`CategoriesApiRepo [updateCategory] Error: `, error);
            throw error;
        }
    }
}