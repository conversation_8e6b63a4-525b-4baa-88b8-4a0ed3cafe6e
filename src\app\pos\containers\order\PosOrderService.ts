import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import {
    KitchenOrderStatusEnum,
    OrderStatusEnum,
    OrderTypeEnum,
    TableStatusEnum,
} from "../../../../common/enums/DataEnums";
import { OrderHelper } from "../../../../common/helpers/OrderHelper";
import { ShiftHelper } from "../../../../common/helpers/ShiftHelper";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IPosOrderModel } from "../../../../common/models/PosOrderModel";
import { IActions } from "../../../../common/redux/data/useActions";
import { debug } from "../../../../common/utils/CommonUtils";
import { IPosOrder } from "../../interface";
import { IPosActions } from "../../redux/usePosActions";
import { PosOrderRepo } from "../../repo/PosOrderRepo";
import { PosPrintersRepo } from "../../repo/PosPrintersRepo";
import { PosTablesRepo } from "../../repo/PosTablesRepo";
import { PosOrderUtils } from "./PosOrderUtils";

export class PosOrderService {
    static resetOrder = (posActions: IPosActions) => {
        posActions.resetOrder();
        posActions.resetHome();
    };

    static handleChequePrinter = async (
        order: IPosOrder | IPosOrderModel,
        actions: IActions
    ) => {
        try {
            if (!this.checkShift()) return;
            actions.setLoading();
            await PosPrintersRepo.printChequeOrder(order);
        } catch (error) {
            debug(`PosOrderService [handleChequePrinter] Error: ${error}`);
            ToastHelper.error(TranslateConstants.ERROR_PRINT_CHEQUE_ORDER);
        } finally {
            actions.setLoading(false);
        }
    };

    static handleKitchenPrinter = async (
        order: IPosOrder | IPosOrderModel,
        orderDiff: IPosOrder | IPosOrderModel,
        actions: IActions,
        posActions: IPosActions,
        isProductsEqual: boolean
    ) => {
        try {
            if (!this.checkShift()) return;
            actions.setLoading();
            if (order.status === OrderStatusEnum.IN_PROGRESS && !isProductsEqual) { // update the DINE_IN order
                const orderModel = order as IPosOrderModel;
                await PosOrderRepo.updateOrder(orderModel);
                if (order.table) {
                    const res = await PosTablesRepo.updateTable(order.table.id, {
                        order: orderModel,
                    });
                    posActions.updateTable(res);
                }
                await PosPrintersRepo.printKitchenOrder(
                    orderDiff,
                    KitchenOrderStatusEnum.EDIT
                );
            } else if (order.status === OrderStatusEnum.PENDING) { // make new DINE_IN order
                order = await PosOrderUtils.handleOrderData(order);
                const res = await PosOrderRepo.addOrder(order);
                const table = PosOrderUtils.handleTableData(res);
                if (table) {
                    await PosTablesRepo.updateTable(table.id, table);
                    posActions.updateTable(table);
                }
                OrderHelper.incrementInvoiceNumber();
                await PosPrintersRepo.printKitchenOrder(
                    order,
                    KitchenOrderStatusEnum.NEW
                );
            }
        } catch (error) {
            debug(`PosOrderService [handleKitchenPrinter] Error: ${error}`);
            ToastHelper.error(TranslateConstants.ERROR_PRINT_KITCHEN_ORDER);
        } finally {
            actions.setLoading(false);
            this.resetOrder(posActions);
        }
    };

    static handleSubmitOrder = async (
        order: IPosOrder | IPosOrderModel,
        cash: number,
        network: number,
        actions: IActions,
        posActions: IPosActions
    ) => {
        try {
            if (!this.checkShift()) return;
            actions.setLoading();
            const formattedOrder = await PosOrderUtils.handleOrderData(
                order,
                cash,
                network
            );
            if (
                order.type === OrderTypeEnum.TAKE_AWAY ||
                order.type === OrderTypeEnum.DELIVERY_APP
            ) {
                await this.handleTakAwayOrderSubmit(formattedOrder);
            } else if (order.type === OrderTypeEnum.DINE_IN) {
                await this.handleDineInOrderSubmit(
                    order as IPosOrderModel,
                    formattedOrder,
                    posActions
                );
            }
        } catch (error) {
            debug(`PosOrderService [handleInvoicePrinter] Error: ${error}`);
            ToastHelper.error(TranslateConstants.ERROR_PRINT_INVOICE_ORDER);
        } finally {
            actions.setLoading(false);
            actions.closeModal();
            this.resetOrder(posActions);
        }
    };

    static handleDeleteOrder = async (
        order: IPosOrder,
        actions: IActions,
        posActions: IPosActions
    ) => {
        try {
            actions.setLoading();
            await PosOrderRepo.deleteOrder(order.id);
            if (order.table) {
                const res = await PosTablesRepo.updateTable(order.table.id, {
                    order: undefined,
                    status: TableStatusEnum.AVAILABLE,
                    startTime: undefined,
                });
                posActions.updateTable(res);
            }
            await PosPrintersRepo.printKitchenOrder(
                order,
                KitchenOrderStatusEnum.DELETE
            );
            this.resetOrder(posActions);
            ToastHelper.success(TranslateConstants.ORDER_DELETED_SUCCESSFULLY);
        } catch (error) {
            debug(`PosOrderService [handleDeleteOrder] Error: ${error}`);
            ToastHelper.error(TranslateConstants.ERROR_DELETE_ORDER);
        } finally {
            actions.setLoading(false);
            actions.closeModal();
        }
    };

    static checkShift() {
        const isShiftOpen = ShiftHelper.isOpen();
        if (!isShiftOpen) {
            ToastHelper.error(TranslateConstants.ERROR_SHIFT_NOT_OPEN);
        }
        return isShiftOpen;
    }

    private static async handleTakAwayOrderSubmit(order: IPosOrder) {
        try {
            await PosOrderRepo.addOrder(order);
            OrderHelper.incrementInvoiceNumber();
            await PosPrintersRepo.printInvoiceOrder(order);
            await PosPrintersRepo.printKitchenOrder(
                order,
                KitchenOrderStatusEnum.NEW
            );
        } catch (error) {
            debug(`PosOrderService [handleTakAwayOrderSubmit] Error: ${error}`);
            throw error;
        }
    }

    private static async handleDineInOrderSubmit(
        order: IPosOrderModel,
        formattedOrder: IPosOrder,
        posActions: IPosActions
    ) {
        try {
            const body = { ...order, ...formattedOrder };
            const table = PosOrderUtils.handleTableData(body);
            await PosOrderRepo.updateOrder(body);
            if (table) await PosTablesRepo.updateTable(table.id, table);
            await PosPrintersRepo.printInvoiceOrder(body);
            if (table) posActions.updateTable(table);
        } catch (error) {
            debug(`PosOrderService [handleTakAwayOrderSubmit] Error: ${error}`);
            throw error;
        }
    }
}