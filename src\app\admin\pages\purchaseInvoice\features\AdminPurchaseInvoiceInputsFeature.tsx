import { <PERSON><PERSON><PERSON>, FC, SetStateAction, useEffect, useMemo } from "react";
import useFetch from "../../../../../common/asyncController/useFetch";
import AddAndFilterComponent from "../../../../../common/components/AddAndFilterComponent";
import DropDownSearchComponent from "../../../../../common/components/DropDownSearchComponent";
import InputComponent from "../../../../../common/components/InputComponent";
import ListComponent from "../../../../../common/components/ListComponent";
import {
    PaymentMethodsConstant,
    VatTypesConstant,
} from "../../../../../common/constants/CommonConstants";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import useScreenSize from "../../../../../common/hooks/useScreenSize";
import { RawMaterialApiRepo } from "../../../../../common/repos/api/RawMaterialApiRepo";
import { SuppliersApiRepo } from "../../../../../common/repos/api/SupplierApiRepo";
import {
    AdminPurchaseInvoiceHeaders,
    AdminPurchaseInvoiceInitialState,
    AdminPurchaseInvoiceInputs,
} from "../AdminPurchaseInvoiceConstants";
import {
    IAdminPurchaseInvoiceInputs,
    IAdminPurchaseInvoiceState,
} from "../AdminPurchaseInvoiceInterfaces";
import { ISupplierModel } from "../../../../../common/models/SuppliersModel";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { AdminPurchaseInvoiceValidation } from "../AdminPurchaseInvoiceValidation";
import { DateUtils } from "../../../../../common/utils/DateUtils";
import useCustomState from "../../../../../common/hooks/useCustomState";
import StatusComponent from "../../../../../common/components/StatusComponent";
import { PurchaseUtils } from "../../../../../common/utils/PurchaseUtils";
import useDerivedState from "../../../../../common/hooks/useDerivedState";
import AdminMultiplePurchasesProductInputsComponent from "../../../components/AdminMultiplePurchasesProductInputsComponent";
import { AdminPurchaseInvoiceService } from "../AdminPurchaseInvoiceService";
import { IPurchaseInvoiceModel } from "../../../../../common/models/PurchaseInvoiceModel";
import { AdminPurchaseInvoiceUtil } from "../AdminPurchaseInvoiceUtil";
import { PurchaseInvoiceApiRepo } from "../../../../../common/repos/api/PurchaseInvoiceApiRepo";
import useActions from "../../../../../common/redux/data/useActions";
import SplitPaymentModal, { ISplitPaymentState, SplitPaymentInitialState } from "../../../../../common/modals/SplitPaymentModal";
import { PaymentsEnum } from "../../../../../common/enums/DataEnums";

interface IProps {
    setIsView: Dispatch<SetStateAction<boolean>>;
    convertedItem: IAdminPurchaseInvoiceState | undefined;
    purchaseOrderId: number | undefined;
}

const AdminPurchaseInvoiceInputsFeature: FC<IProps> = ({
    setIsView,
    convertedItem,
    purchaseOrderId
}) => {
    const { isSm } = useScreenSize();
    const actions = useActions();
    const [state, setState, resetState] = useCustomState<
        IAdminPurchaseInvoiceState,
        IPurchaseInvoiceModel
    >(convertedItem ?? AdminPurchaseInvoiceInitialState, {
        explodeFromReset: ["supplier"],
        resetStateData: AdminPurchaseInvoiceInitialState
    });
    const oldState = useDerivedState(state);

    const {
        data: purchaseInvoiceCount,
        isLoading: purchaseInvoiceCountIsLoading,
        isError: purchaseInvoiceCountIsError,
        refetch: refetchPurchaseInvoiceCount,
    } = useFetch(
        EndPointsEnums.PURCHASE_INVOICES_COUNT,
        PurchaseInvoiceApiRepo.getPurchaseInvoiceCount,
        { autoFetchOnMount: true }
    );

    const {
        data: suppliers,
        isLoading: suppliersIsLoading,
        isError: suppliersIsError,
    } = useFetch(EndPointsEnums.SUPPLIERS, SuppliersApiRepo.getSuppliers);

    const {
        data: rawMaterials,
        isLoading: rawMaterialsIsLoading,
        isError: rawMaterialsIsError,
    } = useFetch(
        EndPointsEnums.RAW_MATERIALS,
        RawMaterialApiRepo.getRawMaterials
    );

    const addPurchaseInvoice = useFlatMutate(
        (body, payment, purchaseOrderId) =>
            PurchaseInvoiceApiRepo.addPurchaseInvoice(body, payment, purchaseOrderId), {
        showDefaultSuccessToast: true,
        updateCached: { key: EndPointsEnums.PURCHASE_INVOICES, operation: "add" },
        closeModalOnSuccess: true,
        onSuccess: async ({ args, data }) => {
            if (args[2]) AdminPurchaseInvoiceService.updatePurchaseOrderConversion(args[2]);
            if (args[3]) await AdminPurchaseInvoiceService.handlePreview(data);
            refetchPurchaseInvoiceCount();
        },
        afterEnd: () => resetState(),
    });

    useEffect(() => {
        setState((prev) => ({
            ...prev,
            ...PurchaseUtils.getPurchaseData(
                prev.purchaseInvoiceProducts,
                prev.isPriceIncludingTax
            ),
        }));
    }, [state.purchaseInvoiceProducts, state.isPriceIncludingTax]);

    const multiInputs = useMemo(() => {
        return AdminPurchaseInvoiceInputs(state.isPriceIncludingTax, rawMaterials);
    }, [rawMaterials, state.isPriceIncludingTax]);

    const handleOnPurchaseInvoiceProductsResult = (
        result: IAdminPurchaseInvoiceInputs[]
    ) => {
        setState((prev) => ({
            ...prev,
            purchaseInvoiceProducts: AdminPurchaseInvoiceUtil.handlePurchaseInvoiceProductsData(
                result,
                prev.isPriceIncludingTax
            ),
        }))
    };

    const handlePayment = (payment: ISplitPaymentState, isPrint: boolean) =>
        addPurchaseInvoice(state, payment, purchaseOrderId, isPrint);

    const handleOnClick = (isPrint: boolean) => {
        if (!AdminPurchaseInvoiceValidation.inputsValidation(state)) return;
        if (state.paymentMethod !== PaymentsEnum.CREDIT) {
            actions.openModal({
                component: (
                    <SplitPaymentModal
                        total={state.total}
                        onClick={(val) => handlePayment(val, isPrint)}
                        paymentType={state.paymentMethod}
                        date={state.dueDate}
                    />
                ),
                title: TranslateConstants.PAY,
                size: "md",
                showButtons: false,
            });
            return;
        }

        handlePayment(SplitPaymentInitialState(state.total, undefined, state.paymentMethod), isPrint);
    };

    return (
        <>
            <AddAndFilterComponent
                onSave={() => handleOnClick(false)}
                onSaveAndPrint={() => handleOnClick(true)}
                onBack={() => setIsView(true)}
            />
            <StatusComponent
                isLoading={
                    purchaseInvoiceCountIsLoading ||
                    suppliersIsLoading ||
                    rawMaterialsIsLoading
                }
                isError={
                    purchaseInvoiceCountIsError ||
                    suppliersIsError ||
                    rawMaterialsIsError
                }
                height={isSm ? 7.5 : 8.1}
                className="!p-0"
            >
                <ListComponent
                    padding="0 px-2"
                    isBorder={true}
                    className="bg-base-100 !border-none"
                >
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                        <InputComponent
                            label={TranslateConstants.INVOICE_NUMBER}
                            type="fixed"
                            value={`${(purchaseInvoiceCount ?? 0) + 1}`}
                        />
                        <InputComponent
                            label={TranslateConstants.DATE}
                            type="datetime-local"
                            value={DateUtils.toIsoString(state.date)}
                            onChange={(date) =>
                                setState({ ...state, date: new Date(date).getTime() })
                            }
                        />
                        <InputComponent
                            label={TranslateConstants.DUE_DATE}
                            type="datetime-local"
                            value={DateUtils.toIsoString(state.dueDate)}
                            onChange={(dueDate) =>
                                setState({ ...state, dueDate: new Date(dueDate).getTime() })
                            }
                        />
                        <DropDownSearchComponent
                            label={TranslateConstants.PAYMENT_METHOD}
                            items={PaymentMethodsConstant}
                            titleSelector={(item) => TranslateHelper.t(item.name)}
                            defaultValue={TranslateHelper.t(state.paymentMethod)}
                            isSearchable={false}
                            onSelect={(item) =>
                                setState({ ...state, paymentMethod: item.value })
                            }
                        />
                        <DropDownSearchComponent
                            label={TranslateConstants.SUPPLIERS}
                            items={suppliers}
                            titleSelector={(item) => item.name}
                            subtitleSelector={(item) => item.mobile}
                            isLoading={suppliersIsLoading}
                            isError={suppliersIsError}
                            defaultValue={state.supplier?.name}
                            onSelect={(supplier: ISupplierModel) =>
                                setState({ ...state, supplier })
                            }
                        />
                        <DropDownSearchComponent
                            label={TranslateConstants.VAT_TYPE}
                            items={VatTypesConstant}
                            titleSelector={(item) => TranslateHelper.t(item.name)}
                            defaultValue={TranslateHelper.t(
                                VatTypesConstant[state.isPriceIncludingTax ? 0 : 1].name
                            )}
                            isSearchable={false}
                            onSelect={(item) =>
                                setState({ ...state, isPriceIncludingTax: item.value })
                            }
                        />
                    </div>
                    <AdminMultiplePurchasesProductInputsComponent<IAdminPurchaseInvoiceInputs>
                        headers={AdminPurchaseInvoiceHeaders}
                        inputs={multiInputs}
                        onResult={handleOnPurchaseInvoiceProductsResult}
                        defaultValues={state.purchaseInvoiceProducts}
                        state={state}
                        setState={setState}
                        resetInputs={
                            state.purchaseInvoiceProducts.length === 0 ||
                            state.isPriceIncludingTax !== oldState.isPriceIncludingTax
                        }
                    />
                </ListComponent>
            </StatusComponent>
        </>
    );
};

export default AdminPurchaseInvoiceInputsFeature;
