import useFetch from "../../../../common/asyncController/useFetch";
import AddAndFilterComponent from "../../../../common/components/AddAndFilterComponent";
import StatusComponent from "../../../../common/components/StatusComponent";
import { EndPointsEnums } from "../../../../common/enums/EndPointsEnums";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import useActions from "../../../../common/redux/data/useActions";
import { AdminPaymentVoucherDataTableHeaders } from "./AdminPaymentVoucherConstants";
import AdminPaymentVoucherModal from "./modals/AdminPaymentVoucherModal";
import { PaymentVouchersApiRepo } from "../../../../common/repos/api/PaymentVouchersApiRepo";
import { IPaymentVoucherModel } from "../../../../common/models/PaymentVoucherModel";
import TableComponent from "../../../../common/components/TableComponent";
import { DateUtils } from "../../../../common/utils/DateUtils";
import { maxStringLength } from "../../../../common/utils/CommonUtils";

const AdminPaymentVoucherPage = () => {
    const actions = useActions();
    const { isSm } = useScreenSize();

    const { data, isLoading, isError, refetch } = useFetch(
        EndPointsEnums.PAYMENT_VOUCHERS,
        PaymentVouchersApiRepo.getPaymentVouchers
    );

    const handleOnClick = (isEdit?: boolean, item?: IPaymentVoucherModel) => {
        actions.openModal({
            component: <AdminPaymentVoucherModal isEdit={isEdit} item={item} />,
            title: isEdit ? TranslateConstants.EDIT : TranslateConstants.ADD,
            size: "md",
            showButtons: false,
        });
    };

    return (
        <>
            <AddAndFilterComponent onAdd={handleOnClick} onReload={refetch} />
            <StatusComponent
                isLoading={isLoading}
                isError={isError}
                isEmpty={!data || data.length === 0}
                height={isSm ? 7.3 : 8}
            >
                <TableComponent
                    headers={AdminPaymentVoucherDataTableHeaders}
                    items={data || []}
                    selectors={(item: IPaymentVoucherModel) => [
                        item.number,
                        item.cash,
                        item.network,
                        DateUtils.format(item.date),
                        maxStringLength(item.note, 10),
                    ]}
                    showEditButton={true}
                    showDeleteButton={false}
                    onEdit={(item: IPaymentVoucherModel) => handleOnClick(true, item)}
                />
            </StatusComponent>
        </>
    );
};

export default AdminPaymentVoucherPage;
