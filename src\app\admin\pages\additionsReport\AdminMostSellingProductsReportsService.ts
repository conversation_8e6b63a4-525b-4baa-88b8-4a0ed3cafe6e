import { IMostSellingProductsModel } from "../../../../common/models/MostSellingProductsModel";

export class AdminAdditionsReportService {
    static handleData(
        additionsData: IMostSellingProductsModel[] = [], 
        returnedData: IMostSellingProductsModel[] = []
    ): IMostSellingProductsModel[] {
        return additionsData.map((el) => {
            const returned = returnedData.find((el2) => el2.name === el.name);
            return {
                name: el.name,
                price: el.price,
                quantity: el.quantity - (returned?.quantity ?? 0),
                subTotal: el.subTotal - (returned?.subTotal ?? 0),
                discount: el.discount - (returned?.discount ?? 0),
                tobaccoTax: el.tobaccoTax - (returned?.tobaccoTax ?? 0),
                vat: el.vat - (returned?.vat ?? 0),
                total: el.total - (returned?.total ?? 0),
            };
            
        });
    }
}