import { FC } from "react";
import { LuTrash2 } from "react-icons/lu";
import ButtonComponent from "../../../../../common/components/ButtonComponent";
import IconButtonComponent from "../../../../../common/components/IconButtonComponent";
import ImageComponent from "../../../../../common/components/ImageComponent";
import NumpadComponent from "../../../../../common/components/NumpadComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { useTranslate } from "../../../../../common/hooks/useTranslate";
import useActions from "../../../../../common/redux/data/useActions";
import { IPosOrderProduct, IPosOrderProductAddition } from "../../../interface";
import usePosActions from "../../../redux/usePosActions";
import { OrderStatusEnum } from "../../../../../common/enums/DataEnums";
import { MdKeyboardArrowDown, MdKeyboardArrowUp } from "react-icons/md";
import PosOrderAdditionsModal from "../modals/PosOrderAdditionsModal";
import PriceComponent from "../../../../../common/components/PriceComponent";

interface IProps {
    productIndex: number;
    product: IPosOrderProduct;
    orderStatus: OrderStatusEnum;
    productsCount: number
}

const PosOrderProductItem: FC<IProps> = ({
    productIndex,
    product,
    orderStatus,
    productsCount
}) => {
    const { translate } = useTranslate();
    const posActions = usePosActions();
    const actions = useActions();
    const isIncludingAdditions = product.product.isIncludingAdditions;

    const handelOnClick = () => {
        if (!isIncludingAdditions) return;
        actions.openModal({
            title:
                translate(TranslateConstants.ADDITIONS) + " (" + product.name + ")",
            component: <PosOrderAdditionsModal productIndex={productIndex} />,
            size: "lg",
        });
    };

    const handleQuantity = (quantity: number) => {
        if (!quantity) return;
        posActions.setOrderProductQuantity({ ...product, quantity }, productIndex);
        actions.closeModal();
    };

    const handleOnQuantityClick = () => {
        actions.openModal({
            component: (
                <NumpadComponent
                    type="number"
                    textCenter={true}
                    button={{
                        text: translate(TranslateConstants.SAVE),
                        onClick: (val) => handleQuantity(val as number),
                    }}
                    showCancelButton={true}
                    onCancelButtonClick={() => actions.closeModal()}
                />
            ),
            showButtons: false,
            size: "sm",
        });
    };

    const handleOnRemove = () => {
        posActions.setOrderProductQuantity(
            { ...product, quantity: 0 },
            productIndex
        );
    };

    const handleOnAdditionQuantityChange = (
        addition: IPosOrderProductAddition,
        quantity: number
    ) => {
        posActions.setOrderProductAdditionQuantity(productIndex, {
            ...addition,
            quantity,
        });
        actions.closeModal();
    };

    const handleOnAdditionQuantityClick = (
        addition: IPosOrderProductAddition
    ) => {
        actions.openModal({
            component: (
                <NumpadComponent
                    type="number"
                    textCenter={true}
                    button={{
                        text: translate(TranslateConstants.SAVE),
                        onClick: (val) =>
                            handleOnAdditionQuantityChange(addition, val as number),
                    }}
                    showCancelButton={true}
                    onCancelButtonClick={() => actions.closeModal()}
                />
            ),
            showButtons: false,
            size: "sm",
        });
    };

    const handleOnAdditionRemove = (addition: IPosOrderProductAddition) => {
        posActions.setOrderProductAdditionQuantity(productIndex, {
            ...addition,
            quantity: 0,
        });
    };

    return (
        <div
            className={
                "flex flex-col" + " " + (!!product.additions?.length ? "gap-1" : "")
            }
        >
            <div
                className={
                    "flex justify-between bg-base-200 border-y p-1 text-sm items-center group" +
                    " " +
                    (isIncludingAdditions ? "cursor-pointer hover:bg-base-300" : "")
                }
                onClick={handelOnClick}
            >
                <div className="flex gap-2">
                    <div className="w-10 h-10 border border-gray-400 rounded-full flex items-center justify-center">
                        <ImageComponent
                            src={product.product.image}
                            className="mask mask-circle"
                        />
                    </div>
                    <div className="flex flex-col">
                        <span>{product.name}</span>
                        <div className="flex items-center">
                            {isIncludingAdditions &&
                                (!!product.additions?.length ? (
                                    <MdKeyboardArrowDown className="text-blue-600 text-lg" />
                                ) : (
                                    <MdKeyboardArrowUp className="text-blue-600 text-lg" />
                                ))}
                            <PriceComponent price={product.price} />
                        </div>
                    </div>
                </div>
                <div className="flex gap-2">
                    <ButtonComponent
                        bgColor="slate"
                        text={product.quantity.toString()}
                        className="!w-12"
                        onClick={handleOnQuantityClick}
                    />
                    {(productsCount > 1) && (
                        <IconButtonComponent
                            bgColor="red"
                            icon={<LuTrash2 />}
                            iconSize="text-lg"
                            className="!w-12 !h-10"
                            onClick={handleOnRemove}
                        />
                    )}
                </div>
            </div>

            {isIncludingAdditions && (
                <div className="flex flex-col gap-1 px-1 border-x-2 border-blue-400">
                    {product.additions?.map((el, index) => (
                        <div
                            key={index}
                            className="flex justify-between items-center text-sm px-2 py-1 rounded border border-gray-300 bg-gray-200"
                        >
                            <div className="flex flex-col">
                                <span>{el.name}</span>
                                {el.price > 0 && <PriceComponent price={el.price} />}
                            </div>
                            <div className="flex gap-2">
                                <ButtonComponent
                                    bgColor="slate"
                                    text={el.quantity.toString()}
                                    className="!w-12"
                                    onClick={() => handleOnAdditionQuantityClick(el)}
                                />
                                {orderStatus === OrderStatusEnum.PENDING && (
                                    <IconButtonComponent
                                        bgColor="red"
                                        icon={<LuTrash2 />}
                                        iconSize="text-lg"
                                        className="!w-12 !h-10"
                                        onClick={() => handleOnAdditionRemove(el)}
                                    />
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default PosOrderProductItem;
