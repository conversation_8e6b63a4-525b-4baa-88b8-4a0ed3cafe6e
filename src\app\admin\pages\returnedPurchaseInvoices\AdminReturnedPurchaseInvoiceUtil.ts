import { IPurchaseOrderModel } from "../../../../common/models/PurchaseOrderModel";
import { fixedNumber } from "../../../../common/utils/numberUtils";
import { PurchaseUtils } from "../../../../common/utils/PurchaseUtils";
import { IAdminReturnedPurchaseInvoiceInputs, IAdminReturnedPurchaseInvoiceState } from "./AdminReturnedPurchaseInvoiceInterfaces";

export class AdminReturnedPurchaseInvoiceUtil {
    static handlePurchaseInvoiceProductsData = (
        purchaseInvoiceProducts: IAdminReturnedPurchaseInvoiceInputs[],
        isPriceIncludingTax: boolean = true
    ): IAdminReturnedPurchaseInvoiceInputs[] => {
        return purchaseInvoiceProducts.map((el) => {
            const { subTotal, vat } = PurchaseUtils.getPurchaseProductData(el, isPriceIncludingTax);
            const id = el.id ?? (el as any)?.item?.id ?? (el as any)?.item?.item?.id ?? undefined; // TODO: fix and remove this

            return {
                ...el,
                id,
                subTotal: fixedNumber(subTotal),
                vat: fixedNumber(vat)
            };
        });
    }

    static convertPurchaseOrderToPurchaseInvoice = (
        purchaseOrder: IPurchaseOrderModel
    ): IAdminReturnedPurchaseInvoiceState => ({
        date: purchaseOrder.date,
        dueDate: purchaseOrder.dueDate,
        paymentMethod: purchaseOrder.paymentMethod,
        supplier: purchaseOrder.supplier,
        isPriceIncludingTax: purchaseOrder.isPriceIncludingTax,
        purchaseInvoiceProducts: purchaseOrder.purchaseOrderProducts.map((item) => ({
            ...item,
            rawMaterial: {
                id: item.rawMaterialId,
                name: item.name,
                price: item.price,
            } as any,
        })),
        note: purchaseOrder.note,
        nativeTotal: purchaseOrder.nativeTotal,
        productsDiscount: purchaseOrder.productsDiscount,
        subTotal: purchaseOrder.subTotal,
        vat: purchaseOrder.vat,
        total: purchaseOrder.total,
    })
}