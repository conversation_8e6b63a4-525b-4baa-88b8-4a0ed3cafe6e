import { useState } from "react";
import ButtonComponent from "./ButtonComponent";
import { IoCheckmarkDoneSharp } from "react-icons/io5";

interface IProps<T> {
    items: T[];
    textSelector: (item: T) => string;
    defaultSelected?: T;
    onSelect?: (item: T) => void;
    isNullable?: boolean;
    className?: string;
}

function SelectedButtonsComponent<T>({
    items,
    textSelector,
    defaultSelected,
    onSelect,
    isNullable = false,
    className = "",
}: IProps<T>) {
    const [selectedItem, setSelectedItem] = useState<T | undefined>(
        defaultSelected
    );

    const handleOnClick = (item: T) => {
        if (selectedItem === item) {
            isNullable && setSelectedItem(undefined);
            return;
        }

        setSelectedItem(item);
        onSelect?.(item);
    };

    return (
        <div className={"flex gap-2" + " " + className}>
            {items.map((item, index) => {
                const isSelected = selectedItem === item;
                return (
                    <ButtonComponent
                        key={index}
                        text={textSelector(item)}
                        iconComponent={isSelected && <IoCheckmarkDoneSharp />}
                        bgColor="transparent"
                        textColor={isSelected ? "primary" : "slate-600"}
                        borderColor={isSelected ? "primary" : "slate-600"}
                        borderWidth="border-2"
                        className={!isSelected ? "dark:border-slate-400 dark:!text-slate-300" : ""}
                        onClick={() => handleOnClick(item)}
                    />
                );
            })}
        </div>
    );
}

export default SelectedButtonsComponent;
