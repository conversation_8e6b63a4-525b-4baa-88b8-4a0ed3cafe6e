import { IOrderModel } from "../../../../common/models/OrderModel";
import { IReturnedOrderModel } from "../../../../common/models/ReturnedOrderModel";

export class AdminCustomersReportService {
    static handleData(
        orders: IOrderModel[] = [],
        returnedOrders: IReturnedOrderModel[] = []
    ): (IOrderModel & { isReturned: boolean, customNumber: string })[] {
        return [
            ...orders.map((el) => ({ ...el, customNumber: el.invoiceNumber, isReturned: false })),
            ...returnedOrders.map((el) => ({
                ...el,
                customNumber: el.returnedInvoiceNumber,
                isReturned: true
            })) as any[],
        ].sort((a, b) => a.invoiceNumber.localeCompare(b.invoiceNumber));
    }
}