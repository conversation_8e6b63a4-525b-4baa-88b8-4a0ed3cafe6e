import { Alignment, Content } from "pdfmake/interfaces";
import { isArabic } from "../utils/CommonUtils";

export class PdfMakeHelper {
    static optionalDocItem = (
        condition: boolean,
        item: Content[],
    ): Content[] => {
        return condition ? [...item] : [];
    };

    static printDivider = (
        isVertical = false,
        bold = false,
        margin = 0
    ): Content => {
        if (isVertical) {
            return {
                stack: Array.from({ length: 5 }).map(() => ({
                    text: "|",
                    bold,
                    margin: [margin, 0, margin, 0],
                })),
            };
        }

        return {
            text: '-'.repeat(bold ? 72 : 78),
            bold,
            margin: [0, margin, 0, margin],
        };
    };

    static textWithBorder = (val: string) => ({
        table: {
            widths: ['*', 'auto', '*'],
            body: [
                [
                    {
                        text: '',
                        border: [false, false, false, false],
                    },
                    {
                        text: val,
                        border: [true, true, true, true],
                        alignment: 'center',
                        fontSize: 18,
                        bold: true,
                        margin: [0, 5, 0, 0],
                    },
                    {
                        text: '',
                        border: [false, false, false, false],
                    },
                ],
            ],
        },
    })

    static longText = (
        val: string,
        maxLength: number,
    ) => {
        if (val.length > maxLength && isArabic(val)) {
            const lines = [];
            let remaining = val;

            while (remaining.length > 0) {
                const match = remaining.match(new RegExp(`^(.{1,${maxLength}})(\\s|$)`));
                if (match) {
                    const [fullMatch] = match;
                    lines.push(fullMatch.trimEnd());
                    remaining = remaining.slice(fullMatch.length).trimStart();
                } else {
                    // No space found within maxLength, so force break at maxLength
                    lines.push(remaining.slice(0, maxLength));
                    remaining = remaining.slice(maxLength).trimStart();
                }
            }

            return {
                stack: [
                    ...lines.map((line) => ({ text: this.reverseTextIfArabic(line), })),
                ],
            }
        }
        return { text: val };
    }

    static titledTextWithBorder = (
        title: string,
        value: string,
        options?: { maxLength?: number; rightBorder?: boolean; leftBorder?: boolean; showBorder?: boolean; }
    ): Content => ({
        color: 'black',
        bold: false,
        lineHeight: 1.2,
        table: {
            widths: ['*', 'auto'],
            body: [
                [
                    this.longText(value ?? '', options?.maxLength || 36),
                    { text: title, bold: true },
                ]
            ]
        },
        layout: {
            hLineWidth: () => options?.showBorder === false ? 0 : 1, // top and bottom borders
            vLineWidth: (i: number) => {
                if (options?.showBorder === false) return 0;
                if (i === 1) return 0; // no border between the two cells
                if (i === 0 && options?.leftBorder === false) return 0;
                return options?.rightBorder === false ? 0 : 1; // left and right borders
            }
        }
    })

    static multiText = (
        val: {
            text: string;
            alignment: Alignment;
            width?: '*' | 'auto';
            fontSize?: number;
            bold?: boolean;
            color?: string;
        }[],
        backGround: boolean = false,
        fontSize: number = 7,
    ) => {
        return {
            layout: 'noBorders',
            style: {
                fillColor: backGround ? 'black' : '',
                color: backGround ? 'white' : '',
                fontSize: fontSize,
            },
            table: {
                widths: [...val.map((v) => v.width ?? '*')],
                body: [
                    [
                        ...val.map((v) => ({
                            text: v.text,
                            alignment: v.alignment,
                            fontSize: v.fontSize,
                            bold: v.bold,
                            color: v.color,
                        })),
                    ],
                ],
            },
        };
    };

    static reverseTextIfArabic = (text: string) => {
        if (isArabic(text)) {
            return ` ${text}`.split(" ").reverse().join(" ");
        }

        return text;
    };
}
