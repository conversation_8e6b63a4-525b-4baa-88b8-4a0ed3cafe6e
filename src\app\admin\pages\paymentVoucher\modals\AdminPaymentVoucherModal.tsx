import { FC } from "react";
import { IAdminPaymentVoucherInputs } from "../AdminPaymentVoucherInterface";
import { AdminPaymentVoucherInputs } from "../AdminPaymentVoucherConstants";
import InputComponent from "../../../../../common/components/InputComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import useCustomState from "../../../../../common/hooks/useCustomState";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { EndPointsEnums } from "../../../../../common/enums/EndPointsEnums";
import { AdminPaymentVoucherValidation } from "../AdminPaymentVoucherValidation";
import { IPaymentVoucherModel } from "../../../../../common/models/PaymentVoucherModel";
import { PaymentVouchersApiRepo } from "../../../../../common/repos/api/PaymentVouchersApiRepo";
import { DateUtils } from "../../../../../common/utils/DateUtils";

interface IProps {
    isEdit?: boolean;
    item?: IPaymentVoucherModel;
}

const AdminPaymentVoucherModal: FC<IProps> = ({ item }) => {
    const [state, setState, resetState] = useCustomState<IAdminPaymentVoucherInputs>(
        AdminPaymentVoucherInputs,
        { updateState: item }
    );
    const addPaymentVoucher = useFlatMutate(PaymentVouchersApiRepo.addPaymentVoucher, {
        updateCached: { key: EndPointsEnums.PAYMENT_VOUCHERS, operation: "add" },
        afterEnd: () => resetState(),
    });
    const updatePaymentVoucher = useFlatMutate(PaymentVouchersApiRepo.updatePaymentVoucher, {
        updateCached: {
            key: EndPointsEnums.PAYMENT_VOUCHERS,
            operation: "update",
            selector: (data: IPaymentVoucherModel) => data.id,
        },
        closeModalOnSuccess: true,
    });

    const onClick = () => {
        const isValid = AdminPaymentVoucherValidation.inputsValidation(state);
        if (!isValid) return;
        if (item) return updatePaymentVoucher(item.id, state);
        addPaymentVoucher(state);
    };

    return (
        <>
            <form className="px-2">
                <InputComponent
                    type="datetime-local"
                    label={TranslateConstants.DATE}
                    onChange={(date) => setState({ ...state, date: new Date(date).getTime() })}
                    value={DateUtils.toIsoString(state.date)}
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.CASH}
                    onChange={(cash) => setState({ ...state, cash: Number(cash) })}
                    value={(state.cash || "").toString()}
                    placeholder="0"
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.NETWORK}
                    onChange={(network) => setState({ ...state, network: Number(network) })}
                    value={(state.network || "").toString()}
                    placeholder="0"
                />
                <InputComponent
                    label={TranslateConstants.STATEMENT}
                    type="textarea"
                    value={state.note || ""}
                    onChange={(note) => setState({ ...state, note })}
                    textAreaRows={2}
                />
            </form>
            <ModalButtonsComponent text={TranslateConstants.SAVE} onClick={onClick} />
        </>
    );
};

export default AdminPaymentVoucherModal;
