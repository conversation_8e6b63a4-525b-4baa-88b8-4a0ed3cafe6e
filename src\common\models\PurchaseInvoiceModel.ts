import { BaseModel } from ".";
import { ISupplierModel } from "./SuppliersModel";

export interface IPurchaseInvoiceProductModel {
    rawMaterialId: number;
    isTaxable: boolean;
    name: string;
    quantity: number;
    price: number;
    discount: number;
    subTotal: number;
    vat: number;
    total: number;
}

export interface IPurchaseInvoiceModel extends BaseModel {
    number: number;
    date: number;
    dueDate: number;
    supplier: ISupplierModel;
    isPriceIncludingTax: boolean;
    purchaseInvoiceProducts: IPurchaseInvoiceProductModel[];
    note: string;
    nativeTotal: number; // the total before any taxes or discounts
    productsDiscount: number; // the total discount on products before taxes
    subTotal: number; // the total before taxes and discounts
    vat: number; // the total of vat
    total: number; // the total after taxes and discounts
    cash: number;
    network: number;
    deferred: number;
    purchaseOrderId?: number;
}