interface IInvoiceAdditionModel {
    name: string;
    quantity: string;
    price: string;
}

export interface IInvoicePrinterItemModel {
    quantity: string;
    product: string;
    price: string;
    additions?: IInvoiceAdditionModel[];
}

export interface IInvoicePdfPrinterBodyModel {
    organizationName: string;
    organizationSubName: string;
    orderType: string;
    invoiceNumber: string;
    orderNumber: string;
    items: IInvoicePrinterItemModel[];
    address?: string;
    vatNo?: string;
    crNo?: string;
    phone?: string;
    invoiceTitle: string;
    subTotal: string;
    discount: string;
    tobaccoTax?: string;
    vat: string;
    total: string;
    nativeTotal: string;
    cash: string;
    network: string;
    totalDeliverApp: string;
    qrCode: string;
    footer?: string;
    customerName?: string;
    customerMobile?: string;
    customerAddress?: string;
    customerTaxNumber?: string;
    invoiceDate: string;
}