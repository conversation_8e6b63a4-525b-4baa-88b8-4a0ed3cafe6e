import { dispatchAction, fetchState } from "../redux/store";
import { updateCachedData } from "./slice";

export class UpdateFetch {
    static update = async <T>(
        key: string,
        updatedData: T,
        selector?: (data: T) => any,
    ) => {
        try {
            dispatchAction(
                updateCachedData({
                    key,
                    operation: selector ? "update" : "updateOne",
                    result: updatedData,
                    selector,
                })
            );
        } catch (error) {
            throw error;
        }
    };

    static dynamicUpdate = async <T>(
        key: string,
        updatedData: (data: T) => Promise<T>
    ) => {
        try {
            const data = fetchState()[key].data;
            if (!data) throw new Error("No data found");
            const result = await updatedData(data);
            dispatchAction(
                updateCachedData({
                    key,
                    operation: "dynamicUpdate",
                    result,
                })
            );
        } catch (error) {
            throw error;
        }
    };
}
